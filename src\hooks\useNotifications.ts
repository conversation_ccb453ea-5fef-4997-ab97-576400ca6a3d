/**
 * Notification Hooks
 * 
 * React Query hooks for managing admin notifications with real-time updates.
 */
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { useEffect } from 'react';
import {
  getAllNotificationCounts,
  subscribeToNotifications,
  markOrdersAsViewed,
  markCustomizationRequestsAsViewed,
  markContactMessagesAsViewed,
  markConsultationRequestsAsViewed,
  NotificationCounts
} from '@/services/notificationService';

// Query keys for notifications
export const notificationKeys = {
  all: ['notifications'] as const,
  counts: () => [...notificationKeys.all, 'counts'] as const,
};

/**
 * Hook to get all notification counts
 */
export function useNotificationCounts() {
  return useQuery({
    queryKey: notificationKeys.counts(),
    queryFn: getAllNotificationCounts,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // Refetch every minute
    refetchOnWindowFocus: true,
  });
}

/**
 * Hook to subscribe to real-time notification updates
 */
export function useRealtimeNotifications() {
  const queryClient = useQueryClient();

  useEffect(() => {
    let unsubscribe: (() => void) | null = null;

    try {
      unsubscribe = subscribeToNotifications((counts: NotificationCounts) => {
        try {
          // Update the query cache with new counts
          queryClient.setQueryData(notificationKeys.counts(), counts);
        } catch (error) {
          console.error('Error updating notification cache:', error);
        }
      });
    } catch (error) {
      console.error('Error setting up realtime notifications:', error);
    }

    return () => {
      if (unsubscribe) {
        try {
          unsubscribe();
        } catch (error) {
          console.error('Error unsubscribing from notifications:', error);
        }
      }
    };
  }, [queryClient]);
}

/**
 * Hook to mark orders as viewed
 */
export function useMarkOrdersAsViewed() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: markOrdersAsViewed,
    onSuccess: () => {
      // Invalidate notification counts to refresh
      queryClient.invalidateQueries({ queryKey: notificationKeys.counts() });
    },
  });
}

/**
 * Hook to mark customization requests as viewed
 */
export function useMarkCustomizationRequestsAsViewed() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: markCustomizationRequestsAsViewed,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: notificationKeys.counts() });
    },
  });
}

/**
 * Hook to mark contact messages as viewed
 */
export function useMarkContactMessagesAsViewed() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: markContactMessagesAsViewed,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: notificationKeys.counts() });
    },
  });
}

/**
 * Hook to mark consultation requests as viewed
 */
export function useMarkConsultationRequestsAsViewed() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: markConsultationRequestsAsViewed,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: notificationKeys.counts() });
    },
  });
}

/**
 * Combined hook that provides all notification functionality
 */
export function useAdminNotifications() {
  const { data: counts, isLoading, error } = useNotificationCounts();

  // Subscribe to real-time updates with error handling
  try {
    useRealtimeNotifications();
  } catch (error) {
    console.error('Error in realtime notifications:', error);
  }

  const markOrdersViewed = useMarkOrdersAsViewed();
  const markCustomizationViewed = useMarkCustomizationRequestsAsViewed();
  const markContactViewed = useMarkContactMessagesAsViewed();
  const markConsultationViewed = useMarkConsultationRequestsAsViewed();

  // Default counts to prevent undefined errors
  const defaultCounts = {
    orders: 0,
    customizationRequests: 0,
    contactMessages: 0,
    consultationRequests: 0
  };

  return {
    counts: counts || defaultCounts,
    isLoading,
    error,
    markOrdersViewed: (markOrdersViewed?.mutate) || (() => {}),
    markCustomizationViewed: (markCustomizationViewed?.mutate) || (() => {}),
    markContactViewed: (markContactViewed?.mutate) || (() => {}),
    markConsultationViewed: (markConsultationViewed?.mutate) || (() => {}),
    isMarkingViewed:
      (markOrdersViewed?.isPending || false) ||
      (markCustomizationViewed?.isPending || false) ||
      (markContactViewed?.isPending || false) ||
      (markConsultationViewed?.isPending || false)
  };
}
