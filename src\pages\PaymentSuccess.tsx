import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { CheckCircle, Loader2, AlertCircle } from 'lucide-react';
import { useAuth } from '@/context/SupabaseAuthContext';
import { useCart } from '@/context/SupabaseCartContext';
import { verifyRazorpayPayment } from '@/services/payment/razorpayService';
import { createOrder, updateOrderWithPayment } from '@/services/orderService';
import { toast } from '@/hooks/use-toast';

/**
 * Payment Success Page
 * Handles mobile payment redirects and verification
 */
const PaymentSuccess: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();
  const { clearCart } = useCart();
  
  const [status, setStatus] = useState<'processing' | 'success' | 'error'>('processing');
  const [message, setMessage] = useState('Processing your payment...');
  const [orderId, setOrderId] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(true);

  useEffect(() => {
    if (!isAuthenticated || !user) {
      navigate('/login');
      return;
    }

    const processPaymentSuccess = async () => {
      try {
        // Get payment details from URL parameters
        const razorpayPaymentId = searchParams.get('razorpay_payment_id');
        const razorpayOrderId = searchParams.get('razorpay_order_id');
        const razorpaySignature = searchParams.get('razorpay_signature');
        const orderIdParam = searchParams.get('order_id');

        console.log('📋 Payment success parameters:', {
          razorpayPaymentId,
          razorpayOrderId,
          razorpaySignature,
          orderIdParam
        });

        if (!razorpayPaymentId || !razorpayOrderId || !razorpaySignature) {
          throw new Error('Missing payment verification parameters');
        }

        // Verify payment
        const isVerified = await verifyRazorpayPayment({
          razorpay_payment_id: razorpayPaymentId,
          razorpay_order_id: razorpayOrderId,
          razorpay_signature: razorpaySignature
        }, orderIdParam);

        if (isVerified) {
          // Handle order creation for temporary orders
          if (orderIdParam && orderIdParam.startsWith('temp_')) {
            console.log('📦 Creating order for temporary payment...');

            // Get stored order data from sessionStorage
            const storedOrderData = sessionStorage.getItem('pendingPaymentOrder');
            const storedCartItems = sessionStorage.getItem('checkoutCart');
            const storedAddresses = sessionStorage.getItem('checkoutAddress');

            if (storedOrderData && storedCartItems && storedAddresses) {
              const orderData = JSON.parse(storedOrderData);
              const cartItems = JSON.parse(storedCartItems);
              const addresses = JSON.parse(storedAddresses);

              // Create the actual order
              const newOrder = await createOrder(
                user.id,
                cartItems,
                orderData.amount,
                'Online Payment',
                addresses.shipping,
                addresses.billing,
                {
                  shippingFee: orderData.shippingFee || 0,
                  isBangaloreDelivery: addresses.shipping?.city?.toLowerCase().includes('bangalore'),
                  shippingNotes: orderData.shippingNotes || '',
                }
              );

              if (newOrder) {
                console.log('✅ Order created successfully:', newOrder.id);

                // Update order with payment details
                await updateOrderWithPayment(newOrder.id, razorpayPaymentId);

                setOrderId(newOrder.id);

                // Clean up session storage
                sessionStorage.removeItem('pendingPaymentOrder');
                sessionStorage.removeItem('checkoutCart');
                sessionStorage.removeItem('checkoutAddress');
              } else {
                throw new Error('Failed to create order after payment');
              }
            } else {
              console.warn('⚠️ Missing stored order data for temporary order');
              setOrderId(orderIdParam);
            }
          } else {
            // For existing orders, just update with payment details
            if (orderIdParam && razorpayPaymentId) {
              await updateOrderWithPayment(orderIdParam, razorpayPaymentId);
            }
            setOrderId(orderIdParam);
          }

          setStatus('success');
          setMessage('Payment successful! Your order has been placed.');

          // Clear cart
          clearCart(false);

          toast({
            title: 'Payment Successful!',
            description: 'Your order has been placed successfully.',
          });

          // Redirect to orders page after 3 seconds
          setTimeout(() => {
            navigate('/orders');
          }, 3000);
        } else {
          throw new Error('Payment verification failed');
        }

      } catch (error) {
        console.error('❌ Error processing payment success:', error);
        setStatus('error');
        setMessage(error instanceof Error ? error.message : 'Failed to process payment. Please contact support if money was deducted.');
        
        toast({
          title: 'Payment Processing Error',
          description: 'There was an issue processing your payment. Please contact support if money was deducted.',
          variant: 'destructive'
        });
      } finally {
        setIsProcessing(false);
      }
    };

    processPaymentSuccess();
  }, [searchParams, isAuthenticated, user, navigate, clearCart]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center">
        {status === 'processing' && (
          <>
            <Loader2 className="h-16 w-16 text-blue-600 animate-spin mx-auto mb-4" />
            <h1 className="text-xl font-semibold text-gray-900 mb-2">Processing Payment</h1>
            <p className="text-gray-600">{message}</p>
          </>
        )}

        {status === 'success' && (
          <>
            <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
            <h1 className="text-xl font-semibold text-gray-900 mb-2">Payment Successful!</h1>
            <p className="text-gray-600 mb-4">{message}</p>
            {orderId && (
              <p className="text-sm text-gray-500">Order ID: {orderId}</p>
            )}
            <p className="text-sm text-gray-500 mt-4">Redirecting to your orders...</p>
          </>
        )}

        {status === 'error' && (
          <>
            <AlertCircle className="h-16 w-16 text-red-600 mx-auto mb-4" />
            <h1 className="text-xl font-semibold text-gray-900 mb-2">Payment Error</h1>
            <p className="text-gray-600 mb-4">{message}</p>
            <button
              type="button"
              onClick={() => navigate('/cart')}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Return to Cart
            </button>
          </>
        )}
      </div>
    </div>
  );
};

export default PaymentSuccess;
