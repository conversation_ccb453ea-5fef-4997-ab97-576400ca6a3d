/**
 * Dashboard Analytics Service
 * 
 * This module provides functions to fetch combined dashboard analytics data.
 */
import { supabase } from '@/lib/supabase';
import { handleSupabaseError, showErrorToast } from '@/utils/supabaseHelpers';
import { DashboardStats, TopProductsSortBy } from './types';
import { getTotalRevenue, getOrderStats, getSalesTrend, getWeeklyPerformance } from './revenueService';
import { 
  getCustomerStats, 
  getCategoryPerformance, 
  getTopSellingProducts,
  getTotalProductsCount,
  getTotalProjectsCount
} from './customerProductService';

/**
 * Get all dashboard statistics in a single call
 * @param topProductsSortBy Sort top products by revenue or quantity
 * @returns Dashboard statistics
 */
export const getDashboardStats = async (
  topProductsSortBy: TopProductsSortBy = 'revenue'
): Promise<DashboardStats> => {
  try {
    const [
      totalRevenue,
      orderStats,
      customerStats,
      categoryPerformance,
      salesTrend,
      weeklyPerformance,
      topSellingProducts,
      totalProducts,
      totalProjects
    ] = await Promise.all([
      getTotalRevenue(),
      getOrderStats(),
      getCustomerStats(),
      getCategoryPerformance(),
      getSalesTrend(),
      getWeeklyPerformance(),
      getTopSellingProducts(5, topProductsSortBy),
      getTotalProductsCount(),
      getTotalProjectsCount()
    ]);

    return {
      totalRevenue,
      orderStats,
      customerStats,
      categoryPerformance,
      salesTrend,
      weeklyPerformance,
      topSellingProducts,
      totalProducts,
      totalProjects
    };
  } catch (error) {
    handleSupabaseError(error, 'getDashboardStats');
    showErrorToast(
      'Error loading dashboard data',
      'Failed to load some dashboard statistics. Please try refreshing the page.'
    );

    // Return empty data
    return {
      totalRevenue: 0,
      orderStats: {
        total: 0,
        completed: 0,
        processing: 0,
        pending: 0
      },
      customerStats: {
        total: 0,
        active: 0,
        inactive: 0,
        totalCustomers: 0
      },
      categoryPerformance: [],
      salesTrend: [],
      weeklyPerformance: [],
      topSellingProducts: [],
      totalProducts: 0,
      totalProjects: 0
    };
  }
};

/**
 * Subscribe to real-time updates for dashboard statistics
 * @param callback Function to call when data changes
 * @returns Unsubscribe function
 */
export const subscribeToRealtimeUpdates = (
  callback: (stats: Partial<DashboardStats>) => void
): (() => void) => {
  const timestamp = Date.now();

  // Subscribe to orders table changes
  const ordersSubscription = supabase
    .channel(`dashboard-orders-${timestamp}`)
    .on('postgres_changes',
      { event: '*', schema: 'public', table: 'orders' },
      async () => {
        try {
          // When orders change, update order stats and total revenue
          const [totalRevenue, orderStats] = await Promise.all([
            getTotalRevenue(),
            getOrderStats()
          ]);

          callback({ totalRevenue, orderStats });
        } catch (error) {
          console.error('Error updating dashboard orders stats:', error);
        }
      }
    )
    .subscribe();

  // Subscribe to order_items table changes
  const orderItemsSubscription = supabase
    .channel(`dashboard-order-items-${timestamp}`)
    .on('postgres_changes',
      { event: '*', schema: 'public', table: 'order_items' },
      async () => {
        try {
          // When order items change, update category performance and top selling products
          const [categoryPerformance, topSellingProducts] = await Promise.all([
            getCategoryPerformance(),
            getTopSellingProducts(5, 'revenue')
          ]);

          callback({ categoryPerformance, topSellingProducts });
        } catch (error) {
          console.error('Error updating dashboard order items stats:', error);
        }
      }
    )
    .subscribe();

  // Subscribe to user_profiles table changes
  const userProfilesSubscription = supabase
    .channel(`dashboard-user-profiles-${timestamp}`)
    .on('postgres_changes',
      { event: '*', schema: 'public', table: 'user_profiles' },
      async () => {
        try {
          // When user profiles change, update customer stats
          const customerStats = await getCustomerStats();
          callback({ customerStats });
        } catch (error) {
          console.error('Error updating dashboard customer stats:', error);
        }
      }
    )
    .subscribe();

  // Subscribe to products table changes
  const productsSubscription = supabase
    .channel(`dashboard-products-${timestamp}`)
    .on('postgres_changes',
      { event: '*', schema: 'public', table: 'products' },
      async () => {
        try {
          // When products change, update total products count
          const totalProducts = await getTotalProductsCount();
          callback({ totalProducts });
        } catch (error) {
          console.error('Error updating dashboard products stats:', error);
        }
      }
    )
    .subscribe();

  // Subscribe to completed_projects and custom_projects table changes
  const projectsSubscription = supabase
    .channel(`dashboard-projects-${timestamp}`)
    .on('postgres_changes',
      { event: '*', schema: 'public', table: 'completed_projects' },
      async () => {
        try {
          // When projects change, update total projects count
          const totalProjects = await getTotalProjectsCount();
          callback({ totalProjects });
        } catch (error) {
          console.error('Error updating dashboard projects stats:', error);
        }
      }
    )
    .subscribe();

  const customProjectsSubscription = supabase
    .channel(`dashboard-custom-projects-${timestamp}`)
    .on('postgres_changes',
      { event: '*', schema: 'public', table: 'custom_projects' },
      async () => {
        try {
          // When custom projects change, update total projects count
          const totalProjects = await getTotalProjectsCount();
          callback({ totalProjects });
        } catch (error) {
          console.error('Error updating dashboard custom projects stats:', error);
        }
      }
    )
    .subscribe();

  // Return unsubscribe function
  return () => {
    ordersSubscription.unsubscribe();
    orderItemsSubscription.unsubscribe();
    userProfilesSubscription.unsubscribe();
    productsSubscription.unsubscribe();
    projectsSubscription.unsubscribe();
    customProjectsSubscription.unsubscribe();
  };
};
