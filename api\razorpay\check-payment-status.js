/**
 * Check Payment Status API
 * Checks the status of a Razorpay payment by order ID
 */

const Razorpay = require('razorpay');
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Initialize Razorpay
const razorpayKeyId = process.env.RAZORPAY_KEY_ID;
const razorpaySecret = process.env.RAZORPAY_KEY_SECRET;

if (!razorpayKeyId || !razorpaySecret) {
  console.error('❌ Missing Razorpay configuration');
}

const razorpay = new Razorpay({
  key_id: razorpayKeyId,
  key_secret: razorpaySecret,
});

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  try {
    const { razorpay_order_id, order_id } = req.body;

    console.log('🔍 Checking payment status for:', {
      razorpay_order_id,
      order_id
    });

    if (!razorpay_order_id) {
      return res.status(400).json({
        success: false,
        error: 'Razorpay order ID is required'
      });
    }

    // Check payment status in Razorpay
    try {
      const order = await razorpay.orders.fetch(razorpay_order_id);
      console.log('📦 Razorpay order status:', {
        id: order.id,
        status: order.status,
        amount_paid: order.amount_paid,
        amount_due: order.amount_due
      });

      // If order is paid, try to get payment details
      if (order.amount_paid > 0) {
        try {
          const payments = await razorpay.orders.fetchPayments(razorpay_order_id);
          console.log('💳 Found payments:', payments.items.length);

          if (payments.items && payments.items.length > 0) {
            const payment = payments.items[0]; // Get the first (latest) payment
            console.log('✅ Payment found:', {
              id: payment.id,
              status: payment.status,
              method: payment.method
            });

            return res.status(200).json({
              success: true,
              payment_status: payment.status,
              payment_id: payment.id,
              payment_method: payment.method,
              amount: payment.amount,
              order_status: order.status
            });
          }
        } catch (paymentError) {
          console.warn('⚠️ Could not fetch payment details:', paymentError.message);
        }
      }

      // Check in our database for payment record
      if (order_id) {
        const { data: paymentRecord, error: dbError } = await supabase
          .from('razorpay_payments')
          .select('*')
          .eq('razorpay_order_id', razorpay_order_id)
          .single();

        if (!dbError && paymentRecord) {
          console.log('📊 Found payment in database:', {
            status: paymentRecord.status,
            payment_id: paymentRecord.razorpay_payment_id
          });

          return res.status(200).json({
            success: true,
            payment_status: paymentRecord.status,
            payment_id: paymentRecord.razorpay_payment_id,
            payment_method: paymentRecord.method,
            amount: paymentRecord.amount,
            order_status: order.status
          });
        }
      }

      // No payment found
      return res.status(200).json({
        success: true,
        payment_status: 'pending',
        order_status: order.status,
        message: 'Payment not completed yet'
      });

    } catch (razorpayError) {
      console.error('❌ Razorpay API error:', razorpayError);
      
      // If order not found in Razorpay, check our database
      if (order_id) {
        const { data: paymentRecord, error: dbError } = await supabase
          .from('razorpay_payments')
          .select('*')
          .eq('razorpay_order_id', razorpay_order_id)
          .single();

        if (!dbError && paymentRecord) {
          return res.status(200).json({
            success: true,
            payment_status: paymentRecord.status,
            payment_id: paymentRecord.razorpay_payment_id,
            payment_method: paymentRecord.method,
            amount: paymentRecord.amount
          });
        }
      }

      return res.status(200).json({
        success: false,
        error: 'Order not found or payment status unavailable'
      });
    }

  } catch (error) {
    console.error('❌ Error checking payment status:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to check payment status'
    });
  }
}
