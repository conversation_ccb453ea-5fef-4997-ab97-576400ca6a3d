-- =====================================================
-- THE BADHEES - COMPLETE VIBES SYSTEM SETUP
-- =====================================================
-- This script sets up the complete emoji reactions (vibes) system
-- Fixes all 406 errors and security definer issues
-- Run this in your Supabase SQL Editor

-- =====================================================
-- 1. CREATE VIBES TABLE
-- =====================================================

-- Drop existing table if it exists (for clean setup)
DROP TABLE IF EXISTS public.vibes CASCADE;

-- Create vibes table
CREATE TABLE public.vibes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    product_id UUID NOT NULL,
    user_id UUID NOT NULL,
    emoji_type TEXT NOT NULL CHECK (emoji_type IN ('love', 'fire', 'star_eyes', 'neutral', 'thumbs_down')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one vibe per user per product
    UNIQUE(product_id, user_id)
);

-- Add foreign key constraints
ALTER TABLE public.vibes 
ADD CONSTRAINT vibes_product_id_fkey 
FOREIGN KEY (product_id) REFERENCES public.products(id) ON DELETE CASCADE;

ALTER TABLE public.vibes 
ADD CONSTRAINT vibes_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- Add indexes for performance
CREATE INDEX idx_vibes_product_id ON public.vibes(product_id);
CREATE INDEX idx_vibes_user_id ON public.vibes(user_id);
CREATE INDEX idx_vibes_emoji_type ON public.vibes(emoji_type);
CREATE INDEX idx_vibes_created_at ON public.vibes(created_at);

-- =====================================================
-- 2. CREATE PRODUCT_VIBES_SUMMARY VIEW (WITHOUT SECURITY DEFINER)
-- =====================================================

-- Drop existing view if it exists
DROP VIEW IF EXISTS public.product_vibes_summary CASCADE;

-- Create view WITHOUT security definer to avoid security warnings
CREATE VIEW public.product_vibes_summary AS
SELECT 
    p.id as product_id,
    COALESCE(v.total_vibes, 0) as total_vibes,
    COALESCE(v.love_count, 0) as love_count,
    COALESCE(v.fire_count, 0) as fire_count,
    COALESCE(v.star_eyes_count, 0) as star_eyes_count,
    COALESCE(v.neutral_count, 0) as neutral_count,
    COALESCE(v.thumbs_down_count, 0) as thumbs_down_count
FROM 
    public.products p
LEFT JOIN (
    SELECT 
        product_id,
        COUNT(*) as total_vibes,
        COUNT(*) FILTER (WHERE emoji_type = 'love') as love_count,
        COUNT(*) FILTER (WHERE emoji_type = 'fire') as fire_count,
        COUNT(*) FILTER (WHERE emoji_type = 'star_eyes') as star_eyes_count,
        COUNT(*) FILTER (WHERE emoji_type = 'neutral') as neutral_count,
        COUNT(*) FILTER (WHERE emoji_type = 'thumbs_down') as thumbs_down_count
    FROM public.vibes
    GROUP BY product_id
) v ON p.id = v.product_id;

-- =====================================================
-- 3. CREATE RPC FUNCTIONS
-- =====================================================

-- Function to get user's vibe for a product
CREATE OR REPLACE FUNCTION public.get_user_vibe(p_product_id UUID, p_user_id UUID)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY INVOKER  -- Use INVOKER instead of DEFINER for better security
AS $$
BEGIN
    RETURN (
        SELECT emoji_type 
        FROM public.vibes 
        WHERE product_id = p_product_id AND user_id = p_user_id
        LIMIT 1
    );
END;
$$;

-- Function to toggle user vibe (add/update/remove)
CREATE OR REPLACE FUNCTION public.toggle_user_vibe(
    p_product_id UUID, 
    p_user_id UUID, 
    p_emoji_type TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY INVOKER  -- Use INVOKER instead of DEFINER for better security
AS $$
DECLARE
    existing_vibe TEXT;
    result BOOLEAN := FALSE;
BEGIN
    -- Check if user already has a vibe for this product
    SELECT emoji_type INTO existing_vibe
    FROM public.vibes 
    WHERE product_id = p_product_id AND user_id = p_user_id;
    
    IF existing_vibe IS NOT NULL THEN
        IF existing_vibe = p_emoji_type THEN
            -- Same emoji clicked, remove the vibe
            DELETE FROM public.vibes 
            WHERE product_id = p_product_id AND user_id = p_user_id;
            result := FALSE; -- Vibe removed
        ELSE
            -- Different emoji clicked, update the vibe
            UPDATE public.vibes 
            SET emoji_type = p_emoji_type, updated_at = NOW()
            WHERE product_id = p_product_id AND user_id = p_user_id;
            result := TRUE; -- Vibe updated
        END IF;
    ELSE
        -- No existing vibe, create new one
        INSERT INTO public.vibes (product_id, user_id, emoji_type)
        VALUES (p_product_id, p_user_id, p_emoji_type);
        result := TRUE; -- Vibe added
    END IF;
    
    RETURN result;
END;
$$;

-- =====================================================
-- 4. SET UP ROW LEVEL SECURITY (RLS)
-- =====================================================

-- Enable RLS on vibes table
ALTER TABLE public.vibes ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view all vibes (for displaying counts)
CREATE POLICY "Anyone can view vibes" ON public.vibes
    FOR SELECT USING (true);

-- Policy: Authenticated users can insert their own vibes
CREATE POLICY "Users can insert their own vibes" ON public.vibes
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policy: Users can update their own vibes
CREATE POLICY "Users can update their own vibes" ON public.vibes
    FOR UPDATE USING (auth.uid() = user_id);

-- Policy: Users can delete their own vibes
CREATE POLICY "Users can delete their own vibes" ON public.vibes
    FOR DELETE USING (auth.uid() = user_id);

-- =====================================================
-- 5. GRANT PERMISSIONS
-- =====================================================

-- Grant permissions to authenticated users
GRANT SELECT ON public.vibes TO authenticated;
GRANT INSERT ON public.vibes TO authenticated;
GRANT UPDATE ON public.vibes TO authenticated;
GRANT DELETE ON public.vibes TO authenticated;

-- Grant permissions on the view
GRANT SELECT ON public.product_vibes_summary TO anon;
GRANT SELECT ON public.product_vibes_summary TO authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION public.get_user_vibe(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.toggle_user_vibe(UUID, UUID, TEXT) TO authenticated;

-- =====================================================
-- 6. CREATE TRIGGER FOR UPDATED_AT
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;

-- Trigger to automatically update updated_at
CREATE TRIGGER update_vibes_updated_at
    BEFORE UPDATE ON public.vibes
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- =====================================================
-- 7. INSERT SAMPLE DATA (OPTIONAL - FOR TESTING)
-- =====================================================

-- Uncomment the following lines if you want to add sample vibes for testing
-- Make sure to replace the UUIDs with actual product and user IDs from your database

/*
-- Sample vibes (replace with actual UUIDs)
INSERT INTO public.vibes (product_id, user_id, emoji_type) VALUES
    ('your-product-id-1', 'your-user-id-1', 'love'),
    ('your-product-id-1', 'your-user-id-2', 'fire'),
    ('your-product-id-2', 'your-user-id-1', 'star_eyes')
ON CONFLICT (product_id, user_id) DO NOTHING;
*/

-- =====================================================
-- SETUP COMPLETE!
-- =====================================================

-- Verify the setup
SELECT 'Vibes system setup completed successfully!' as status;

-- Check if view is working
SELECT COUNT(*) as total_products_with_vibes_view FROM public.product_vibes_summary;

-- Check if functions are created
SELECT 'Functions created: get_user_vibe, toggle_user_vibe' as functions_status;
