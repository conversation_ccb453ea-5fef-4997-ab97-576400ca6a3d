/**
 * <PERSON><PERSON>pay Create Order API
 * Creates a new Razorpay order for payment processing
 */

import Razorpay from 'razorpay';

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // <PERSON>le preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  try {
    console.log('🚀 [Create Order] Starting order creation...');

    // Validate environment variables
    const razorpayKeyId = process.env.RAZORPAY_KEY_ID;
    const razorpaySecret = process.env.RAZORPAY_SECRET;

    if (!razorpayKeyId || !razorpaySecret) {
      console.error('❌ [Create Order] Missing Razorpay credentials');
      return res.status(500).json({
        success: false,
        error: 'Payment gateway configuration error'
      });
    }

    // Initialize Razorpay
    const razorpay = new Razorpay({
      key_id: razorpayKeyId,
      key_secret: razorpaySecret,
    });

    // Extract request data
    const { amount, currency = 'INR', receipt, notes = {} } = req.body;

    // Validate required fields
    if (!amount || !receipt) {
      console.error('❌ [Create Order] Missing required fields');
      return res.status(400).json({
        success: false,
        error: 'Amount and receipt are required'
      });
    }

    // Validate amount
    if (isNaN(amount) || amount <= 0) {
      console.error('❌ [Create Order] Invalid amount:', amount);
      return res.status(400).json({
        success: false,
        error: 'Invalid amount provided'
      });
    }

    // Create Razorpay order
    const orderOptions = {
      amount: Math.round(amount * 100), // Convert to paise
      currency,
      receipt,
      notes,
      payment_capture: 1 // Auto capture payment
    };

    console.log('📝 [Create Order] Creating order with options:', {
      amount: orderOptions.amount,
      currency: orderOptions.currency,
      receipt: orderOptions.receipt,
      notesCount: Object.keys(notes).length
    });

    const order = await razorpay.orders.create(orderOptions);

    console.log('✅ [Create Order] Order created successfully:', {
      id: order.id,
      amount: order.amount,
      currency: order.currency,
      status: order.status
    });

    return res.status(200).json({
      success: true,
      data: {
        id: order.id,
        amount: order.amount,
        currency: order.currency,
        receipt: order.receipt,
        status: order.status,
        created_at: order.created_at
      }
    });

  } catch (error) {
    console.error('❌ [Create Order] Error:', error);
    
    return res.status(500).json({
      success: false,
      error: 'Failed to create payment order',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}
