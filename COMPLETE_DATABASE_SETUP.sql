-- ===========================================
-- THE BADHEES - COMPLETE DATABASE SETUP
-- ===========================================
-- This file contains the complete database schema and setup for The Badhees furniture ecommerce website
-- Execute this file in your Supabase SQL editor to set up the entire database

-- ===========================================
-- 1. ENABLE REQUIRED EXTENSIONS
-- ===========================================
-- Enable UUID extension for generating unique IDs
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ===========================================
-- 2. CREATE TABLES
-- ===========================================

-- Categories table
CREATE TABLE IF NOT EXISTS categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    image_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Products table
CREATE TABLE IF NOT EXISTS products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    sale_price DECIMAL(10,2),
    is_sale BOOLEAN DEFAULT FALSE,
    category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
    stock_quantity INTEGER DEFAULT 0,
    sku VARCHAR(100) UNIQUE,
    weight DECIMAL(8,2),
    dimensions JSONB,
    material VARCHAR(255),
    color VARCHAR(100),
    is_featured BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Product images table
CREATE TABLE IF NOT EXISTS product_images (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    image_url TEXT NOT NULL,
    alt_text VARCHAR(255),
    display_order INTEGER DEFAULT 0,
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User profiles table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    name VARCHAR(255),
    phone VARCHAR(20),
    role VARCHAR(20) DEFAULT 'customer' CHECK (role IN ('customer', 'admin', 'employee')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Orders table
CREATE TABLE IF NOT EXISTS orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'shipped', 'delivered', 'canceled', 'paid')),
    payment_method VARCHAR(50),
    payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')),
    total_amount DECIMAL(10,2) NOT NULL,
    shipping_address JSONB NOT NULL,
    billing_address JSONB,
    shipping_fee DECIMAL(10,2) DEFAULT 0,
    is_bangalore_delivery BOOLEAN,
    shipping_notes TEXT,
    razorpay_payment_id VARCHAR(255),
    razorpay_order_id VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Order items table
CREATE TABLE IF NOT EXISTS order_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    quantity INTEGER NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Shopping cart table
CREATE TABLE IF NOT EXISTS shopping_cart (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    quantity INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, product_id)
);

-- Product ratings table
CREATE TABLE IF NOT EXISTS product_ratings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    review TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(product_id, user_id)
);

-- Contact submissions table
CREATE TABLE IF NOT EXISTS contact_submissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    subject VARCHAR(255),
    message TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'new' CHECK (status IN ('new', 'in_progress', 'resolved')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Customization requests table
CREATE TABLE IF NOT EXISTS customization_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    project_type VARCHAR(100),
    budget_range VARCHAR(50),
    timeline VARCHAR(50),
    description TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'new' CHECK (status IN ('new', 'in_progress', 'quoted', 'approved', 'completed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Consultation requests table
CREATE TABLE IF NOT EXISTS consultation_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    consultation_type VARCHAR(100),
    preferred_date DATE,
    preferred_time TIME,
    message TEXT,
    status VARCHAR(20) DEFAULT 'new' CHECK (status IN ('new', 'scheduled', 'completed', 'canceled')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Completed projects table
CREATE TABLE IF NOT EXISTS completed_projects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    location VARCHAR(255),
    completion_date DATE,
    client_name VARCHAR(255),
    project_value DECIMAL(12,2),
    is_featured BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Project images table
CREATE TABLE IF NOT EXISTS project_images (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID REFERENCES completed_projects(id) ON DELETE CASCADE,
    image_url TEXT NOT NULL,
    alt_text VARCHAR(255),
    display_order INTEGER DEFAULT 0,
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Razorpay payments table
CREATE TABLE IF NOT EXISTS razorpay_payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID REFERENCES orders(id) ON DELETE SET NULL,
    razorpay_order_id VARCHAR(255) NOT NULL,
    razorpay_payment_id VARCHAR(255),
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'INR',
    status VARCHAR(20) DEFAULT 'created' CHECK (status IN ('created', 'captured', 'failed', 'refunded')),
    method VARCHAR(50),
    user_id UUID REFERENCES user_profiles(id) ON DELETE SET NULL,
    error_description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===========================================
-- 3. CREATE INDEXES FOR PERFORMANCE
-- ===========================================
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_products_featured ON products(is_featured);
CREATE INDEX IF NOT EXISTS idx_products_active ON products(is_active);
CREATE INDEX IF NOT EXISTS idx_product_images_product ON product_images(product_id);
CREATE INDEX IF NOT EXISTS idx_orders_user ON orders(user_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_order_items_order ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_shopping_cart_user ON shopping_cart(user_id);
CREATE INDEX IF NOT EXISTS idx_product_ratings_product ON product_ratings(product_id);
CREATE INDEX IF NOT EXISTS idx_razorpay_payments_order ON razorpay_payments(razorpay_order_id);

-- ===========================================
-- 4. INSERT SAMPLE CATEGORIES
-- ===========================================
INSERT INTO categories (name, description) VALUES
('Living Room', 'Comfortable and stylish furniture for your living space'),
('Bedroom', 'Complete bedroom furniture sets and accessories'),
('Dining Room', 'Elegant dining tables, chairs, and storage solutions'),
('Office', 'Professional and ergonomic office furniture'),
('Storage', 'Innovative storage solutions for every room'),
('Outdoor', 'Weather-resistant outdoor furniture and decor'),
('Decor', 'Home accessories and decorative items'),
('Lighting', 'Modern and traditional lighting solutions')
ON CONFLICT (name) DO NOTHING;

-- ===========================================
-- 5. CREATE ADMIN USER PROMOTION FUNCTION
-- ===========================================
CREATE OR REPLACE FUNCTION promote_user_to_admin(user_email TEXT)
RETURNS VOID AS $$
BEGIN
    UPDATE user_profiles 
    SET role = 'admin', updated_at = NOW()
    WHERE email = user_email;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'User with email % not found', user_email;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ===========================================
-- 6. CREATE DEMOTE ADMIN FUNCTION
-- ===========================================
CREATE OR REPLACE FUNCTION demote_admin_to_customer(user_email TEXT)
RETURNS VOID AS $$
BEGIN
    UPDATE user_profiles 
    SET role = 'customer', updated_at = NOW()
    WHERE email = user_email;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'User with email % not found', user_email;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ===========================================
-- 7. ENABLE ROW LEVEL SECURITY (RLS)
-- ===========================================
-- Enable RLS on all tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE shopping_cart ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_ratings ENABLE ROW LEVEL SECURITY;
ALTER TABLE contact_submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE customization_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE consultation_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE completed_projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE razorpay_payments ENABLE ROW LEVEL SECURITY;

-- ===========================================
-- 8. CREATE RLS POLICIES
-- ===========================================
-- User profiles policies
CREATE POLICY "Users can view their own profile" ON user_profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update their own profile" ON user_profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Admins can view all profiles" ON user_profiles FOR ALL USING (
    EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
);

-- Products policies (public read, admin write)
CREATE POLICY "Anyone can view active products" ON products FOR SELECT USING (is_active = true);
CREATE POLICY "Admins can manage products" ON products FOR ALL USING (
    EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
);

-- Categories policies (public read, admin write)
CREATE POLICY "Anyone can view categories" ON categories FOR SELECT USING (true);
CREATE POLICY "Admins can manage categories" ON categories FOR ALL USING (
    EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
);

-- Orders policies
CREATE POLICY "Users can view their own orders" ON orders FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create their own orders" ON orders FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Admins can view all orders" ON orders FOR ALL USING (
    EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
);

-- Shopping cart policies
CREATE POLICY "Users can manage their own cart" ON shopping_cart FOR ALL USING (auth.uid() = user_id);

-- Product reviews table (for detailed reviews with comments)
CREATE TABLE IF NOT EXISTS product_reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    title VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(product_id, user_id)
);

-- Product vibes table (for emoji reactions)
CREATE TABLE IF NOT EXISTS vibes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    emoji_type VARCHAR(20) NOT NULL CHECK (emoji_type IN ('love', 'fire', 'star_eyes', 'neutral', 'thumbs_down')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(product_id, user_id)
);

-- Enable RLS on new tables
ALTER TABLE product_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE vibes ENABLE ROW LEVEL SECURITY;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_product_reviews_product ON product_reviews(product_id);
CREATE INDEX IF NOT EXISTS idx_product_reviews_user ON product_reviews(user_id);
CREATE INDEX IF NOT EXISTS idx_product_reviews_created_at ON product_reviews(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_vibes_product ON vibes(product_id);
CREATE INDEX IF NOT EXISTS idx_vibes_user ON vibes(user_id);
CREATE INDEX IF NOT EXISTS idx_vibes_emoji_type ON vibes(emoji_type);
CREATE INDEX IF NOT EXISTS idx_vibes_product_emoji ON vibes(product_id, emoji_type);

-- RLS Policies for product_reviews
CREATE POLICY "Anyone can view product reviews" ON product_reviews FOR SELECT USING (true);
CREATE POLICY "Authenticated users can create reviews" ON product_reviews FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own reviews" ON product_reviews FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own reviews" ON product_reviews FOR DELETE USING (auth.uid() = user_id);
CREATE POLICY "Admins can manage all reviews" ON product_reviews FOR ALL USING (
    EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
);

-- RLS Policies for vibes
CREATE POLICY "Anyone can view vibes" ON vibes FOR SELECT USING (true);
CREATE POLICY "Authenticated users can create vibes" ON vibes FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own vibes" ON vibes FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own vibes" ON vibes FOR DELETE USING (auth.uid() = user_id);
CREATE POLICY "Admins can manage all vibes" ON vibes FOR ALL USING (
    EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
);

-- Create view for product vibe summary
CREATE OR REPLACE VIEW product_vibes_summary AS
SELECT
    product_id,
    COUNT(*) as total_vibes,
    COUNT(CASE WHEN emoji_type = 'love' THEN 1 END) as love_count,
    COUNT(CASE WHEN emoji_type = 'fire' THEN 1 END) as fire_count,
    COUNT(CASE WHEN emoji_type = 'star_eyes' THEN 1 END) as star_eyes_count,
    COUNT(CASE WHEN emoji_type = 'neutral' THEN 1 END) as neutral_count,
    COUNT(CASE WHEN emoji_type = 'thumbs_down' THEN 1 END) as thumbs_down_count
FROM vibes
GROUP BY product_id;

-- Create view for product ratings summary (for backward compatibility)
CREATE OR REPLACE VIEW product_ratings_summary AS
SELECT
    product_id,
    COALESCE(AVG(rating), 0)::DECIMAL(3,2) as average_rating,
    COUNT(rating) as review_count
FROM product_reviews
GROUP BY product_id;

-- Function to get user's vibe for a product
CREATE OR REPLACE FUNCTION get_user_vibe(p_product_id UUID, p_user_id UUID)
RETURNS TEXT AS $$
DECLARE
    user_emoji TEXT;
BEGIN
    SELECT emoji_type INTO user_emoji
    FROM vibes
    WHERE product_id = p_product_id AND user_id = p_user_id;

    RETURN user_emoji;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to toggle user vibe (add or update)
CREATE OR REPLACE FUNCTION toggle_user_vibe(p_product_id UUID, p_user_id UUID, p_emoji_type TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    existing_emoji TEXT;
BEGIN
    -- Check if user already has a vibe for this product
    SELECT emoji_type INTO existing_emoji
    FROM vibes
    WHERE product_id = p_product_id AND user_id = p_user_id;

    IF existing_emoji IS NOT NULL THEN
        IF existing_emoji = p_emoji_type THEN
            -- Same emoji clicked, remove the vibe
            DELETE FROM vibes
            WHERE product_id = p_product_id AND user_id = p_user_id;
            RETURN FALSE; -- Vibe removed
        ELSE
            -- Different emoji clicked, update the vibe
            UPDATE vibes
            SET emoji_type = p_emoji_type, updated_at = NOW()
            WHERE product_id = p_product_id AND user_id = p_user_id;
            RETURN TRUE; -- Vibe updated
        END IF;
    ELSE
        -- No existing vibe, create new one
        INSERT INTO vibes (product_id, user_id, emoji_type)
        VALUES (p_product_id, p_user_id, p_emoji_type);
        RETURN TRUE; -- Vibe added
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Enable realtime for vibes table
ALTER PUBLICATION supabase_realtime ADD TABLE vibes;

-- ===========================================
-- 9. SETUP COMPLETE
-- ===========================================
-- Database setup is now complete!
--
-- To promote a user to admin, run:
-- SELECT promote_user_to_admin('<EMAIL>');
--
-- To demote an admin to customer, run:
-- SELECT demote_admin_to_customer('<EMAIL>');
