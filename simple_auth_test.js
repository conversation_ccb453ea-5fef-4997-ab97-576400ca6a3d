/**
 * Simple Authentication Test Script
 * Copy and paste this into browser console after refreshing the page
 */

// Simple test function that doesn't rely on window.supabase
const simpleAuthTest = async () => {
  console.log('🔍 SIMPLE AUTHENTICATION TEST');
  console.log('=' .repeat(40));
  
  try {
    // Check if we're on the right page
    console.log('📍 Current URL:', window.location.href);
    console.log('📍 Origin:', window.location.origin);
    
    // Check if Supabase is available
    if (typeof window.supabase === 'undefined') {
      console.error('❌ window.supabase is not available');
      console.log('💡 This means Supabase client is not exposed globally');
      console.log('💡 Please refresh the page and try again');
      return { success: false, error: 'Supabase not available' };
    }
    
    console.log('✅ window.supabase is available');
    
    // Test basic Supabase connection
    try {
      const { data: { session }, error: sessionError } = await window.supabase.auth.getSession();
      
      if (sessionError) {
        console.error('❌ Session error:', sessionError);
        return { success: false, error: sessionError.message };
      }
      
      if (!session || !session.user) {
        console.log('⚠️ No active session found');
        console.log('💡 Please log in first and then run this test');
        return { success: false, error: 'No active session' };
      }
      
      console.log('✅ Active session found:', {
        userId: session.user.id,
        email: session.user.email,
        expiresAt: session.expires_at
      });
      
      // Test basic database connection
      console.log('🔍 Testing database connection...');
      const { data: userProfile, error: profileError } = await window.supabase
        .from('user_profiles')
        .select('*')
        .eq('id', session.user.id)
        .single();
      
      if (profileError) {
        console.error('❌ Database connection failed:', profileError);
        return { success: false, error: `Database: ${profileError.message}` };
      }
      
      console.log('✅ Database connection successful');
      console.log('✅ User profile:', {
        id: userProfile.id,
        email: userProfile.email,
        role: userProfile.role
      });
      
      // Test orders table access
      console.log('🔍 Testing orders table access...');
      const { data: orders, error: ordersError } = await window.supabase
        .from('orders')
        .select('id, status, created_at')
        .eq('user_id', session.user.id)
        .limit(1);
      
      if (ordersError) {
        console.error('❌ Orders table access failed:', ordersError);
        return { success: false, error: `Orders RLS: ${ordersError.message}` };
      }
      
      console.log('✅ Orders table access successful');
      console.log('📋 User orders count:', orders?.length || 0);
      
      return { 
        success: true, 
        session: session,
        userProfile: userProfile,
        ordersCount: orders?.length || 0
      };
      
    } catch (authError) {
      console.error('❌ Authentication test failed:', authError);
      return { success: false, error: authError.message };
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    return { success: false, error: error.message };
  }
};

// Test if user is logged in
const checkLoginStatus = async () => {
  console.log('🔍 CHECKING LOGIN STATUS');
  console.log('=' .repeat(30));
  
  if (typeof window.supabase === 'undefined') {
    console.error('❌ Supabase not available. Please refresh the page.');
    return false;
  }
  
  try {
    const { data: { session } } = await window.supabase.auth.getSession();
    
    if (!session || !session.user) {
      console.log('❌ Not logged in');
      console.log('💡 Please log in at: ' + window.location.origin + '/login');
      return false;
    }
    
    console.log('✅ Logged in as:', session.user.email);
    return true;
    
  } catch (error) {
    console.error('❌ Error checking login status:', error);
    return false;
  }
};

// Quick RLS test
const quickRLSTest = async () => {
  console.log('🔍 QUICK RLS TEST');
  console.log('=' .repeat(20));
  
  if (typeof window.supabase === 'undefined') {
    console.error('❌ Supabase not available');
    return false;
  }
  
  try {
    const { data: { session } } = await window.supabase.auth.getSession();
    
    if (!session) {
      console.error('❌ No session');
      return false;
    }
    
    // Try to insert a test order with valid status
    const testOrder = {
      user_id: session.user.id,
      status: 'pending', // Use valid status instead of 'test'
      payment_method: 'online',
      payment_status: 'pending',
      total_amount: 1.00,
      shipping_address: { test: true },
      billing_address: { test: true }
    };
    
    const { data, error } = await window.supabase
      .from('orders')
      .insert([testOrder])
      .select()
      .single();
    
    if (error) {
      console.error('❌ RLS Test Failed:', error.message);
      return false;
    }
    
    console.log('✅ RLS Test Passed - Order created:', data.id);
    
    // Clean up
    await window.supabase.from('orders').delete().eq('id', data.id);
    console.log('✅ Test order cleaned up');
    
    return true;
    
  } catch (error) {
    console.error('❌ RLS test error:', error);
    return false;
  }
};

// Make functions globally available
window.simpleAuthTest = simpleAuthTest;
window.checkLoginStatus = checkLoginStatus;
window.quickRLSTest = quickRLSTest;

console.log('🔧 Simple Auth Test Script Loaded!');
console.log('');
console.log('Available commands:');
console.log('1. checkLoginStatus() - Check if user is logged in');
console.log('2. simpleAuthTest() - Run comprehensive auth test');
console.log('3. quickRLSTest() - Test RLS policies');
console.log('');
console.log('💡 Make sure to refresh the page first, then run these tests');
