import { supabase } from '@/lib/supabase';
import { toast } from '@/hooks/use-toast';
import { CartItem } from '@/context/SupabaseCartContext';

import {
  sendOrderConfirmationEmail,
  sendPaymentSuccessEmail,
  sendOrderStatusUpdateEmail
} from '@/services/emailService';

export interface OrderItem {
  id: string;
  order_id: string;
  product_id: string;
  quantity: number;
  price: number;
  product?: {
    name: string;
    image_url?: string;
    images?: Array<{image_url: string}>;
  };
}

export interface Order {
  id: string;
  user_id: string;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  payment_method: string;
  payment_status?: 'pending' | 'completed' | 'failed' | 'refunded';
  razorpay_order_id?: string;
  razorpay_payment_id?: string;

  total_amount: number;
  shipping_address?: {
    name: string;
    street: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  };
  billing_address?: {
    name: string;
    street: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  };
  // Shipping information
  shipping_fee?: number;
  is_bangalore_delivery?: boolean | null;
  shipping_notes?: string;
  created_at: string;
  updated_at?: string;
  order_items?: OrderItem[];
  customer_name?: string; // For admin display
  user_profiles?: {
    display_name?: string;
    email?: string;
  };
  user?: Array<{
    display_name?: string;
    email?: string;
  }>;
}

// Type for database response
interface DbOrder extends Omit<Order, 'order_items'> {
  order_items?: DbOrderItem[];
}

interface DbOrderItem {
  id: string;
  order_id: string;
  product_id: string;
  quantity: number;
  price: number;
  product?: {
    name?: string;
    images?: Array<{image_url?: string}>;
  };
}

/**
 * Creates a new order in the database
 * @param userId The user ID
 * @param cartItems The cart items
 * @param totalAmount The total order amount
 * @param paymentMethod The payment method
 * @param shippingAddress The shipping address
 * @param billingAddress The billing address
 * @param shippingInfo Optional shipping information
 * @returns The created order or null if creation failed
 */
export const createOrder = async (
  userId: string,
  cartItems: CartItem[],
  totalAmount: number,
  paymentMethod: string,
  shippingAddress?: {
    name: string;
    street: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  },
  billingAddress?: {
    name: string;
    street: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  },
  shippingInfo?: {
    shippingFee: number;
    isBangaloreDelivery: boolean | null;
    shippingNotes: string;
  }
): Promise<Order | null> => {
  try {
    // Create the order
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .insert([
        {
          user_id: userId,
          status: 'pending',
          payment_method: paymentMethod,
          total_amount: totalAmount,
          shipping_address: shippingAddress,
          billing_address: billingAddress || shippingAddress,
          // Shipping information
          shipping_fee: shippingInfo?.shippingFee || 0,
          is_bangalore_delivery: shippingInfo?.isBangaloreDelivery,
          shipping_notes: shippingInfo?.shippingNotes || ''
        }
      ])
      .select()
      .single();

    if (orderError) {
      console.error('Error creating order:', orderError);
      toast({
        title: 'Error creating order',
        description: orderError.message || 'An unexpected error occurred',
        variant: 'destructive',
      });
      return null;
    }

    // Create order items
    const orderItems = cartItems.map(item => ({
      order_id: order.id,
      product_id: item.product.id,
      quantity: item.quantity,
      price: item.product.isSale && item.product.salePrice ? item.product.salePrice : item.product.price
    }));

    const { error: itemsError } = await supabase
      .from('order_items')
      .insert(orderItems);

    if (itemsError) {
      console.error('Error creating order items:', itemsError);
      toast({
        title: 'Error creating order items',
        description: itemsError.message || 'An unexpected error occurred',
        variant: 'destructive',
      });
      // Consider rolling back the order here
      return null;
    }

    // Don't show success toast here - will be shown after payment confirmation

    // Get user email
    const { data: userData, error: userError } = await supabase
      .from('user_profiles')
      .select('email')
      .eq('id', userId)
      .single();

    // Send order confirmation email
    if (!userError && userData?.email) {
      sendOrderConfirmationEmail(order.id, userData.email)
        .catch(error => console.error('Error sending order confirmation email:', error));
    }

    return order;
  } catch (error) {
    console.error('Error in createOrder:', error);
    toast({
      title: 'Error placing order',
      description: 'An unexpected error occurred while placing your order.',
      variant: 'destructive',
    });
    return null;
  }
};

/**
 * Creates an order AFTER successful payment confirmation
 * @param userId The user ID
 * @param cartItems Array of cart items
 * @param totalAmount Total order amount
 * @param paymentMethod Payment method
 * @param shippingAddress Shipping address
 * @param billingAddress Billing address
 * @param shippingInfo Shipping information
 * @param razorpayPaymentId Razorpay payment ID
 * @param razorpayOrderId Razorpay order ID
 * @returns The created order or null if failed
 */
export const createOrderAfterPayment = async (
  userId: string,
  cartItems: CartItem[],
  totalAmount: number,
  paymentMethod: string,
  shippingAddress?: {
    name: string;
    street: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  },
  billingAddress?: {
    name: string;
    street: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  },
  shippingInfo?: {
    shippingFee: number;
    isBangaloreDelivery: boolean | null;
    shippingNotes: string;
  },
  razorpayPaymentId: string,
  razorpayOrderId: string
): Promise<Order | null> => {
  try {
    console.log('📦 [createOrderAfterPayment] Starting order creation with data:', {
      userId,
      cartItemsCount: cartItems?.length || 0,
      totalAmount,
      paymentMethod,
      razorpayPaymentId,
      razorpayOrderId
    });

    // Test database connection first
    console.log('🔍 [createOrderAfterPayment] Testing database connection...');
    try {
      const { data: connectionTest, error: connectionError } = await supabase
        .from('orders')
        .select('id')
        .limit(1);

      if (connectionError) {
        console.error('❌ [createOrderAfterPayment] Database connection test failed:', connectionError);
        throw new Error('Database connection failed: ' + connectionError.message);
      }

      console.log('✅ [createOrderAfterPayment] Database connection test successful');
    } catch (connErr) {
      console.error('❌ [createOrderAfterPayment] Database connection error:', connErr);
      throw new Error('Unable to connect to database: ' + connErr.message);
    }

    // Validate required data
    if (!userId) {
      throw new Error('User ID is required for order creation');
    }

    if (!cartItems || cartItems.length === 0) {
      throw new Error('Cart items are required for order creation');
    }

    if (!totalAmount || totalAmount <= 0) {
      throw new Error('Valid total amount is required for order creation');
    }

    if (!razorpayPaymentId) {
      throw new Error('Razorpay payment ID is required for order creation');
    }
    // Create the order with payment details (with timeout)
    console.log('💾 [createOrderAfterPayment] Inserting order into database...');

    const orderInsertPromise = supabase
      .from('orders')
      .insert([
        {
          user_id: userId,
          status: 'processing', // Set as processing since payment is confirmed but order needs to be processed
          payment_method: paymentMethod,
          payment_status: 'completed', // Use 'completed' as per new schema
          total_amount: totalAmount,
          shipping_address: shippingAddress,
          billing_address: billingAddress || shippingAddress,
          // Shipping information
          shipping_fee: shippingInfo?.shippingFee || 0,
          is_bangalore_delivery: shippingInfo?.isBangaloreDelivery,
          shipping_notes: shippingInfo?.shippingNotes || '',
          // Payment details
          razorpay_payment_id: razorpayPaymentId,
          razorpay_order_id: razorpayOrderId
        }
      ])
      .select()
      .single();

    // Add timeout to prevent hanging
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Order creation timeout after 30 seconds')), 30000);
    });

    let { data: order, error: orderError } = await Promise.race([
      orderInsertPromise,
      timeoutPromise
    ]) as any;

    if (orderError) {
      console.error('❌ [createOrderAfterPayment] Error creating order:', orderError);

      // If it's a timeout error, try one more time with a simpler approach
      if (orderError.message?.includes('timeout')) {
        console.log('🔄 [createOrderAfterPayment] Retrying order creation due to timeout...');
        try {
          const { data: retryOrder, error: retryError } = await supabase
            .from('orders')
            .insert([
              {
                user_id: userId,
                status: 'processing',
                payment_status: 'completed',
                total_amount: totalAmount,
                shipping_address: shippingAddress,
                billing_address: billingAddress || shippingAddress,
                shipping_fee: shippingInfo?.shippingFee || 0,
                is_bangalore_delivery: shippingInfo?.isBangaloreDelivery,
                shipping_notes: shippingInfo?.shippingNotes || '',
                razorpay_payment_id: razorpayPaymentId,
                razorpay_order_id: razorpayOrderId
              }
            ])
            .select()
            .single();

          if (retryError) {
            throw retryError;
          }

          console.log('✅ [createOrderAfterPayment] Order created successfully on retry:', retryOrder.id);
          order = retryOrder;
          orderError = null; // Clear the error since retry succeeded
        } catch (retryErr) {
          console.error('❌ [createOrderAfterPayment] Retry also failed:', retryErr);
          toast({
            title: 'Error creating order',
            description: 'Order creation failed after retry. Please contact support.',
            variant: 'destructive',
          });
          return null;
        }
      } else {
        toast({
          title: 'Error creating order',
          description: orderError.message || 'An unexpected error occurred',
          variant: 'destructive',
        });
        return null;
      }
    } else {
      console.log('✅ [createOrderAfterPayment] Order created successfully:', order.id);
    }

    // Create order items with timeout
    console.log('📦 [createOrderAfterPayment] Creating order items...');
    const orderItems = cartItems.map(item => ({
      order_id: order.id,
      product_id: item.product.id,
      quantity: item.quantity,
      price: item.product.isSale && item.product.salePrice ? item.product.salePrice : item.product.price,
      total: (item.product.isSale && item.product.salePrice ? item.product.salePrice : item.product.price) * item.quantity
    }));

    const itemsInsertPromise = supabase
      .from('order_items')
      .insert(orderItems);

    const itemsTimeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Order items creation timeout after 20 seconds')), 20000);
    });

    const { error: itemsError } = await Promise.race([
      itemsInsertPromise,
      itemsTimeoutPromise
    ]) as any;

    if (itemsError) {
      console.error('❌ [createOrderAfterPayment] Error creating order items:', itemsError);

      // If it's a timeout error, try one more time
      if (itemsError.message?.includes('timeout')) {
        console.log('🔄 [createOrderAfterPayment] Retrying order items creation due to timeout...');
        try {
          const { error: retryItemsError } = await supabase
            .from('order_items')
            .insert(orderItems);

          if (retryItemsError) {
            throw retryItemsError;
          }

          console.log('✅ [createOrderAfterPayment] Order items created successfully on retry');
        } catch (retryErr) {
          console.error('❌ [createOrderAfterPayment] Order items retry also failed:', retryErr);
          toast({
            title: 'Error creating order items',
            description: 'Order items creation failed after retry. Order created but items missing.',
            variant: 'destructive',
          });
          // Don't return null here - order was created, just items failed
        }
      } else {
        toast({
          title: 'Error creating order items',
          description: itemsError.message || 'An unexpected error occurred',
          variant: 'destructive',
        });
        // Don't return null here - order was created, just items failed
      }
    } else {
      console.log('✅ [createOrderAfterPayment] Order items created successfully');
    }

    // Store payment record in payments table for admin tracking (with timeout)
    console.log('💾 [createOrderAfterPayment] Storing payment record in payments...');

    const paymentInsertPromise = supabase
      .from('payments')
      .insert({
        razorpay_payment_id: razorpayPaymentId,
        razorpay_order_id: razorpayOrderId,
        order_id: order.id,
        amount: totalAmount,
        currency: 'INR',
        status: 'captured',
        method: 'online', // Will be updated by webhook if needed
        user_id: userId,
        webhook_verified: true
      });

    const paymentTimeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Payment record creation timeout after 15 seconds')), 15000);
    });

    const { error: paymentError } = await Promise.race([
      paymentInsertPromise,
      paymentTimeoutPromise
    ]) as any;

    if (paymentError) {
      console.error('Error storing payment record:', paymentError);
      // Don't fail the order creation for this
    }

    // Show success toast
    toast({
      title: 'Order placed successfully',
      description: `Your order #${order.id.substring(0, 8)} has been placed and payment confirmed.`,
    });

    // Get user email
    const { data: userData, error: userError } = await supabase
      .from('user_profiles')
      .select('email')
      .eq('id', userId)
      .single();

    // Send order confirmation email
    if (!userError && userData?.email) {
      sendOrderConfirmationEmail(order.id, userData.email)
        .catch(error => console.error('Error sending order confirmation email:', error));
    }

    return order;
  } catch (error) {
    console.error('❌ [createOrderAfterPayment] Critical error:', error);

    // Store failed order data in localStorage for recovery
    const failedOrderData = {
      userId,
      cartItems,
      totalAmount,
      paymentMethod,
      shippingAddress,
      billingAddress,
      shippingInfo,
      razorpayPaymentId,
      razorpayOrderId,
      timestamp: new Date().toISOString(),
      error: error.message
    };

    try {
      localStorage.setItem('failed_order_data', JSON.stringify(failedOrderData));
      console.log('💾 [createOrderAfterPayment] Failed order data stored in localStorage for recovery');
    } catch (storageError) {
      console.error('❌ [createOrderAfterPayment] Failed to store order data in localStorage:', storageError);
    }

    toast({
      title: 'Order creation failed',
      description: 'Payment was successful but order creation failed. Please contact support with your payment ID: ' + razorpayPaymentId,
      variant: 'destructive',
    });
    return null;
  }
};

/**
 * Gets all orders for a user
 * @param userId The user ID
 * @returns Array of orders
 */
export const getUserOrders = async (userId: string): Promise<Order[]> => {
  try {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        order_items:order_items(
          *,
          product:products(name, images:product_images(image_url))
        )
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      // Check if the error is related to the foreign key relationship
      if (error.message?.includes('foreign key') ||
          error.message?.includes('relationship') ||
          error.message?.includes('auth.users')) {
        console.error('Foreign key relationship error in getUserOrders:', error);
        console.log('This can be fixed by running the fix_orders_user_relationship.sql migration');

        // Return empty array to avoid breaking the UI
        return [];
      }

      console.error('Error fetching user orders:', error);
      return [];
    }

    // Process the data to format it correctly
    return data.map((order: DbOrder) => {
      // Format order items to include product details
      const orderItems = (order.order_items || []).map((item: DbOrderItem) => ({
        ...item,
        product: {
          name: item.product?.name || 'Unknown Product',
          image_url: item.product?.images?.[0]?.image_url || ''
        }
      }));

      return {
        ...order,
        order_items: orderItems
      };
    });
  } catch (error) {
    console.error('Error in getUserOrders:', error);
    return [];
  }
};

/**
 * Gets all orders (admin function)
 * @param status Optional status filter
 * @returns Array of orders
 */
export const getAllOrders = async (status?: string): Promise<Order[]> => {
  try {
    console.log('🔍 [getAllOrders] Starting fetch with status filter:', status);

    // Check current user authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError) {
      console.error('❌ [getAllOrders] Auth error:', authError);
      return [];
    }

    if (!user) {
      console.error('❌ [getAllOrders] No authenticated user');
      return [];
    }

    console.log('👤 [getAllOrders] Current user:', user.id, user.email);

    // Check user profile and role
    const { data: userProfile, error: profileError } = await supabase
      .from('user_profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.error('❌ [getAllOrders] Error fetching user profile:', profileError);
    } else {
      console.log('👤 [getAllOrders] User role:', userProfile?.role);
    }

    // Build the query step by step
    let query = supabase
      .from('orders')
      .select(`
        *,
        order_items:order_items(
          *,
          product:products(
            name,
            images:product_images(image_url)
          )
        )
      `)
      .order('created_at', { ascending: false });

    // Apply status filter if provided
    if (status && status !== 'all') {
      query = query.eq('status', status);
    }

    console.log('🔍 [getAllOrders] Executing query...');
    const { data, error } = await query;

    if (error) {
      console.error('❌ [getAllOrders] Error fetching orders:', error);
      console.error('❌ [getAllOrders] Error details:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code
      });
      throw error;
    }

    if (!data) {
      console.log('⚠️ [getAllOrders] No orders found (data is null)');
      return [];
    }

    console.log(`✅ [getAllOrders] Found ${data.length} orders`);

    // Get user profiles separately to avoid join issues
    const userIds = [...new Set(data.map(order => order.user_id))];
    console.log('👥 [getAllOrders] Fetching profiles for user IDs:', userIds.length);

    const { data: userProfiles, error: userError } = await supabase
      .from('user_profiles')
      .select('id, display_name, email')
      .in('id', userIds);

    if (userError) {
      console.warn('⚠️ [getAllOrders] Error fetching user profiles:', userError);
    } else {
      console.log('👥 [getAllOrders] Found user profiles:', userProfiles?.length || 0);
    }

    // Create a map of user profiles
    const userProfileMap = (userProfiles || []).reduce((acc: Record<string, any>, profile: any) => {
      acc[profile.id] = profile;
      return acc;
    }, {});

    // Process the data to format it correctly
    const processedOrders = data.map((order: DbOrder) => {
      // Format order items to include product details
      const orderItems = (order.order_items || []).map((item: DbOrderItem) => ({
        ...item,
        product: {
          name: item.product?.name || 'Unknown Product',
          image_url: item.product?.images?.[0]?.image_url || ''
        }
      }));

      // Get customer name from user profiles
      const userProfile = userProfileMap[order.user_id];
      const customerName = userProfile?.display_name || userProfile?.email || `Customer ${order.user_id.substring(0, 8)}`;

      return {
        ...order,
        order_items: orderItems,
        customer_name: customerName
      };
    });

    console.log('✅ [getAllOrders] Processed orders:', processedOrders.length);
    console.log('📊 [getAllOrders] Sample order:', processedOrders[0] ? {
      id: processedOrders[0].id,
      status: processedOrders[0].status,
      customer_name: processedOrders[0].customer_name,
      total_amount: processedOrders[0].total_amount
    } : 'No orders');

    return processedOrders;

  } catch (error) {
    console.error('❌ [getAllOrders] Unexpected error:', error);
    return [];
  }
};

/**
 * Gets a single order by ID
 * @param orderId The order ID
 * @returns The order or null if not found
 */
export const getOrderById = async (orderId: string): Promise<Order | null> => {
  try {
    console.log('Fetching order by ID:', orderId);

    // Get order without user_profiles join to avoid issues
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        order_items:order_items(
          *,
          product:products(
            name,
            images:product_images(image_url)
          )
        )
      `)
      .eq('id', orderId)
      .single();

    if (error) {
      console.error('Error fetching order:', error);
      return null;
    }

    if (!data) {
      console.log('Order not found');
      return null;
    }

    // Get user profile separately
    const { data: userProfile, error: userError } = await supabase
      .from('user_profiles')
      .select('display_name, email')
      .eq('id', data.user_id)
      .single();

    if (userError) {
      console.warn('Error fetching user profile:', userError);
    }

    // Format order items to include product details
    const orderItems = (data.order_items || []).map((item: DbOrderItem) => ({
      ...item,
      product: {
        name: item.product?.name || 'Unknown Product',
        image_url: item.product?.images?.[0]?.image_url || ''
      }
    }));

    // Get customer name from user profile
    const customerName = userProfile?.display_name || userProfile?.email || `Customer ${data.user_id.substring(0, 8)}`;

    return {
      ...data,
      order_items: orderItems,
      customer_name: customerName
    };

  } catch (error) {
    console.error('Error in getOrderById:', error);
    return null;
  }
};

/**
 * Updates an order's status
 * @param orderId The order ID
 * @param status The new status
 * @returns True if successful, false otherwise
 */
export const updateOrderStatus = async (
  orderId: string,
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
): Promise<boolean> => {
  try {
    // Add updated_at timestamp to track when the status was changed
    const updateData: any = {
      status,
      updated_at: new Date().toISOString()
    };

    // If status is 'processing', also update payment_status to completed
    if (status === 'processing') {
      updateData.payment_status = 'completed';
    }

    const { error } = await supabase
      .from('orders')
      .update(updateData)
      .eq('id', orderId);

    if (error) {
      // Check for specific error types
      if (error.code === 'PGRST202') {
        console.error('Function not found error in updateOrderStatus:', error);
        toast({
          title: 'Database configuration error',
          description: 'There appears to be an issue with the database configuration. Please contact support.',
          variant: 'destructive',
        });
      } else if (error.code === '42501') {
        console.error('Permission denied error in updateOrderStatus:', error);
        toast({
          title: 'Permission denied',
          description: 'You do not have permission to update this order. Please check your account privileges.',
          variant: 'destructive',
        });
      } else {
        console.error('Error updating order status:', error);
        toast({
          title: 'Error updating order',
          description: error.message || 'An unexpected error occurred',
          variant: 'destructive',
        });
      }
      return false;
    }

    toast({
      title: 'Order updated',
      description: `Order status has been updated to ${status}.`,
    });

    // Send email notification for certain status changes
    if (['shipped', 'delivered', 'cancelled'].includes(status)) {
      try {
        // Get order details to get user ID
        const { data: orderData, error: orderFetchError } = await supabase
          .from('orders')
          .select('user_id')
          .eq('id', orderId)
          .single();

        if (!orderFetchError) {
          // Get user email
          const { data: userData, error: userError } = await supabase
            .from('user_profiles')
            .select('email')
            .eq('id', orderData.user_id)
            .single();

          // Send order status update email
          if (!userError && userData?.email) {
            sendOrderStatusUpdateEmail(orderId, userData.email, status)
              .catch(error => console.error('Error sending order status update email:', error));
          }
        }
      } catch (emailError) {
        console.error('Error sending order status update email:', emailError);
        // Don't return false here, as the order status was successfully updated
      }
    }

    return true;
  } catch (error) {
    console.error('Error in updateOrderStatus:', error);
    toast({
      title: 'Error updating order',
      description: 'An unexpected error occurred while updating the order status.',
      variant: 'destructive',
    });
    return false;
  }
};

/**
 * Updates an order with Razorpay payment details
 * @param orderId The order ID
 * @param razorpayPaymentId The Razorpay payment ID
 * @returns True if successful, false otherwise
 */
export const updateOrderWithPayment = async (
  orderId: string,
  razorpayPaymentId: string
): Promise<boolean> => {
  try {
    console.log('💳 [updateOrderWithPayment] Starting update for order:', orderId, 'with payment ID:', razorpayPaymentId);

    // Get order details first to get user ID
    const { data: orderData, error: orderFetchError } = await supabase
      .from('orders')
      .select('user_id, status, payment_status, total_amount')
      .eq('id', orderId)
      .single();

    if (orderFetchError) {
      console.error('❌ [updateOrderWithPayment] Error fetching order for payment update:', orderFetchError);
      return false;
    }

    console.log('📋 [updateOrderWithPayment] Current order data:', orderData);

    // Update the order with payment details
    console.log('💾 [updateOrderWithPayment] Updating order with payment details...');
    const updateData = {
      razorpay_payment_id: razorpayPaymentId,
      payment_status: 'completed',
      status: 'processing',
      updated_at: new Date().toISOString()
    };
    console.log('📝 [updateOrderWithPayment] Update data:', updateData);

    const { data: updatedOrder, error } = await supabase
      .from('orders')
      .update(updateData)
      .eq('id', orderId)
      .select();

    if (error) {
      console.error('❌ [updateOrderWithPayment] Error updating order with payment details:', error);
      return false;
    }

    if (!updatedOrder || updatedOrder.length === 0) {
      console.error('❌ [updateOrderWithPayment] No order found with ID:', orderId);
      return false;
    }

    console.log('✅ [updateOrderWithPayment] Order updated successfully:', updatedOrder[0]);

    // Store payment record in payments table for admin tracking
    console.log('💾 [updateOrderWithPayment] Storing payment record...');
    const { error: paymentError } = await supabase
      .from('payments')
      .upsert({
        razorpay_payment_id: razorpayPaymentId,
        razorpay_order_id: `order_${orderId}`, // Generate razorpay order ID if not available
        order_id: orderId,
        amount: orderData.total_amount,
        currency: 'INR',
        status: 'captured',
        method: 'upi', // Assuming UPI for mobile payments
        user_id: orderData.user_id,
        webhook_verified: true
      }, {
        onConflict: 'razorpay_payment_id'
      });

    if (paymentError) {
      console.error('⚠️ [updateOrderWithPayment] Error storing payment record:', paymentError);
      // Don't fail the entire operation for this
    } else {
      console.log('✅ [updateOrderWithPayment] Payment record stored successfully');
    }

    // Get user email
    console.log('👤 [updateOrderWithPayment] Fetching user email for notifications...');
    const { data: userData, error: userError } = await supabase
      .from('user_profiles')
      .select('email')
      .eq('id', orderData.user_id)
      .single();

    if (userError) {
      console.error('⚠️ [updateOrderWithPayment] Error fetching user email:', userError);
    } else {
      console.log('📧 [updateOrderWithPayment] User email found:', userData?.email);
    }

    // Send payment success email
    const isDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
    if (!userError && userData?.email) {
      if (isDevelopment) {
        console.log('📧 Email sending disabled in development mode');
        console.log('📧 Would send payment success email to:', userData?.email, 'for order:', orderId, 'payment:', razorpayPaymentId);
      } else {
        // Only send emails in production
        sendPaymentSuccessEmail(orderId, userData.email, razorpayPaymentId)
          .catch(error => console.error('Error sending payment success email:', error));
      }
    }

    console.log('🎉 [updateOrderWithPayment] Payment update completed successfully');

    // Trigger a custom event to notify components about the order update
    window.dispatchEvent(new CustomEvent('order-updated', {
      detail: { orderId, paymentId: razorpayPaymentId }
    }));

    return true;
  } catch (error) {
    console.error('❌ [updateOrderWithPayment] Error in updateOrderWithPayment:', error);
    return false;
  }
};




