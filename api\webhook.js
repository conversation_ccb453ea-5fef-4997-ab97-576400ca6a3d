/**
 * Razorpay Webhook Handler
 * Handles Razorpay webhook events for payment confirmation
 */

import crypto from 'crypto';
import { createClient } from '@supabase/supabase-js';

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, X-Razorpay-Signature');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  try {
    console.log('🔔 [Webhook] Received Razorpay webhook');

    // Validate environment variables
    const webhookSecret = process.env.RAZORPAY_WEBHOOK_SECRET;
    const supabaseUrl = process.env.VITE_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

    if (!webhookSecret) {
      console.error('❌ [Webhook] Missing webhook secret');
      return res.status(500).json({
        success: false,
        error: 'Webhook configuration error'
      });
    }

    // Get the signature from headers
    const receivedSignature = req.headers['x-razorpay-signature'];
    if (!receivedSignature) {
      console.error('❌ [Webhook] Missing signature header');
      return res.status(400).json({
        success: false,
        error: 'Missing webhook signature'
      });
    }

    // Verify webhook signature
    const body = JSON.stringify(req.body);
    const expectedSignature = crypto
      .createHmac('sha256', webhookSecret)
      .update(body)
      .digest('hex');

    const isValidSignature = crypto.timingSafeEqual(
      Buffer.from(receivedSignature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );

    if (!isValidSignature) {
      console.error('❌ [Webhook] Invalid signature');
      return res.status(400).json({
        success: false,
        error: 'Invalid webhook signature'
      });
    }

    console.log('✅ [Webhook] Signature verified');

    // Process the webhook event
    const event = req.body.event;
    const payload = req.body.payload;

    console.log('📋 [Webhook] Event type:', event);

    // Initialize Supabase client if available
    let supabase = null;
    if (supabaseUrl && supabaseServiceKey) {
      supabase = createClient(supabaseUrl, supabaseServiceKey);
    }

    // Handle different webhook events
    switch (event) {
      case 'payment.captured':
        await handlePaymentCaptured(payload.payment.entity, supabase);
        break;
      
      case 'payment.failed':
        await handlePaymentFailed(payload.payment.entity, supabase);
        break;
      
      case 'order.paid':
        await handleOrderPaid(payload.order.entity, payload.payment.entity, supabase);
        break;
      
      default:
        console.log(`ℹ️ [Webhook] Unhandled event type: ${event}`);
    }

    return res.status(200).json({
      success: true,
      message: 'Webhook processed successfully'
    });

  } catch (error) {
    console.error('❌ [Webhook] Error:', error);
    return res.status(500).json({
      success: false,
      error: 'Webhook processing failed'
    });
  }
}

// Handle payment captured event
async function handlePaymentCaptured(payment, supabase) {
  try {
    console.log('💰 [Webhook] Processing payment.captured');
    console.log('💰 [Webhook] Payment ID:', payment.id);
    console.log('💰 [Webhook] Order ID:', payment.order_id);

    if (!supabase) {
      console.warn('⚠️ [Webhook] Supabase not available, skipping database update');
      return;
    }

    // Update payment record
    const { error: paymentError } = await supabase
      .from('payments')
      .update({
        razorpay_payment_id: payment.id,
        status: 'captured',
        method: payment.method,
        webhook_verified: true,
        updated_at: new Date().toISOString()
      })
      .eq('razorpay_order_id', payment.order_id);

    if (paymentError) {
      console.error('❌ [Webhook] Payment update error:', paymentError);
      return;
    }

    // Get the order ID from payment record
    const { data: paymentRecord, error: fetchError } = await supabase
      .from('payments')
      .select('order_id')
      .eq('razorpay_order_id', payment.order_id)
      .single();

    if (fetchError || !paymentRecord) {
      console.error('❌ [Webhook] Could not fetch payment record:', fetchError);
      return;
    }

    // Update order status
    const { error: orderError } = await supabase
      .from('orders')
      .update({
        payment_status: 'completed',
        razorpay_payment_id: payment.id,
        updated_at: new Date().toISOString()
      })
      .eq('id', paymentRecord.order_id);

    if (orderError) {
      console.error('❌ [Webhook] Order update error:', orderError);
    } else {
      console.log('✅ [Webhook] Payment captured and order updated');
    }

  } catch (error) {
    console.error('❌ [Webhook] Error handling payment.captured:', error);
  }
}

// Handle payment failed event
async function handlePaymentFailed(payment, supabase) {
  try {
    console.log('❌ [Webhook] Processing payment.failed');
    console.log('❌ [Webhook] Payment ID:', payment.id);
    console.log('❌ [Webhook] Error:', payment.error_description);

    if (!supabase) {
      console.warn('⚠️ [Webhook] Supabase not available, skipping database update');
      return;
    }

    // Update payment record
    const { error } = await supabase
      .from('payments')
      .update({
        razorpay_payment_id: payment.id,
        status: 'failed',
        failure_reason: payment.error_description,
        webhook_verified: true,
        updated_at: new Date().toISOString()
      })
      .eq('razorpay_order_id', payment.order_id);

    if (error) {
      console.error('❌ [Webhook] Payment failed update error:', error);
    } else {
      console.log('✅ [Webhook] Payment failure recorded');
    }

  } catch (error) {
    console.error('❌ [Webhook] Error handling payment.failed:', error);
  }
}

// Handle order paid event
async function handleOrderPaid(order, payment, supabase) {
  try {
    console.log('🎉 [Webhook] Processing order.paid');
    console.log('🎉 [Webhook] Order ID:', order.id);
    console.log('🎉 [Webhook] Payment ID:', payment.id);

    // This event is fired when an order is fully paid
    // We can use this for additional business logic if needed
    console.log('🎉 [Webhook] Order paid event processed');

  } catch (error) {
    console.error('❌ [Webhook] Error handling order.paid:', error);
  }
}
