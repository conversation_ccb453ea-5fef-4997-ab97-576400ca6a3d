/**
 * Query Razorpay Order API
 * Fetches payment details for a specific Razorpay order
 */

const Razorpay = require('razorpay');

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  try {
    console.log('🔍 [Query Order] Starting Razorpay order query...');

    // Validate environment variables
    const razorpayKeyId = process.env.RAZORPAY_KEY_ID;
    const razorpaySecret = process.env.RAZORPAY_SECRET;

    if (!razorpayKeyId || !razorpaySecret) {
      console.error('❌ [Query Order] Missing Razorpay credentials');
      return res.status(500).json({
        success: false,
        error: 'Razorpay configuration error'
      });
    }

    // Extract order ID
    const { order_id } = req.body;

    if (!order_id) {
      console.error('❌ [Query Order] Missing order ID');
      return res.status(400).json({
        success: false,
        error: 'Order ID is required'
      });
    }

    console.log('📝 [Query Order] Querying order:', order_id);

    // Initialize Razorpay
    const razorpay = new Razorpay({
      key_id: razorpayKeyId,
      key_secret: razorpaySecret,
    });

    // Fetch order details
    const order = await razorpay.orders.fetch(order_id);
    console.log('📦 [Query Order] Order details:', {
      id: order.id,
      status: order.status,
      amount: order.amount,
      currency: order.currency
    });

    // Fetch payments for this order
    const payments = await razorpay.orders.fetchPayments(order_id);
    console.log('💳 [Query Order] Found payments:', payments.items.length);

    if (payments.items.length > 0) {
      const paymentDetails = payments.items.map(payment => ({
        id: payment.id,
        status: payment.status,
        amount: payment.amount,
        method: payment.method,
        created_at: payment.created_at
      }));

      console.log('✅ [Query Order] Payment details:', paymentDetails);

      return res.status(200).json({
        success: true,
        order: {
          id: order.id,
          status: order.status,
          amount: order.amount,
          currency: order.currency
        },
        payments: paymentDetails
      });
    } else {
      console.log('⚠️ [Query Order] No payments found for order');
      return res.status(200).json({
        success: true,
        order: {
          id: order.id,
          status: order.status,
          amount: order.amount,
          currency: order.currency
        },
        payments: []
      });
    }

  } catch (error) {
    console.error('❌ [Query Order] Error:', error);
    
    return res.status(500).json({
      success: false,
      error: 'Failed to query order details',
      details: error.message
    });
  }
}
