
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import AdminSidebar from "@/components/admin/AdminSidebar";
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell
} from "@/components/ui/table";
import { Button } from '@/components/ui/button';
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from "@/components/ui/dialog";
import { Search, ArrowUpDown, User, History, Loader2, ShoppingBag, Calendar, Mail, Phone, RefreshCw } from 'lucide-react';
import { useAuth } from '@/context/SupabaseAuthContext';
import { getCustomers, getCustomerOrders, Customer } from '@/services/customerService';
import { toast } from '@/hooks/use-toast';
import { supabase } from '@/lib/supabase';

const AdminCustomers = () => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortField, setSortField] = useState('name');
  const [sortDirection, setSortDirection] = useState('asc');
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [isOrderHistoryOpen, setIsOrderHistoryOpen] = useState(false);
  const [customerOrders, setCustomerOrders] = useState<any[]>([]);
  const [isLoadingOrders, setIsLoadingOrders] = useState(false);
  const navigate = useNavigate();
  const { isAuthenticated, isAdmin } = useAuth();

  // Fetch customers from Supabase
  const fetchCustomers = async () => {
    try {
      setIsLoading(true);

      const data = await getCustomers();
      setCustomers(data);
    } catch (error) {
      console.error('Error fetching customers:', error);
      toast({
        title: 'Error',
        description: 'Failed to load customers. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    window.scrollTo(0, 0);

    // Check if user has access to this page
    if (!isAuthenticated) {
      navigate('/login?redirect=/admin/customers');
    } else if (!isAdmin()) {
      navigate('/');
    } else {
      // Fetch customers from Supabase
      fetchCustomers();
    }
  }, [isAuthenticated, isAdmin, navigate]);

  // Set up real-time subscription to user profile and order changes
  useEffect(() => {
    if (!isAuthenticated || !isAdmin()) return;

    // Subscribe to changes in the user_profiles table
    const userProfilesSubscription = supabase
      .channel('user-profiles-changes')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'user_profiles', filter: 'role=eq.user' },
        () => {
          console.log('User profiles data changed, refetching customers...');
          fetchCustomers();
        }
      )
      .subscribe();

    // Subscribe to changes in the orders table
    const ordersSubscription = supabase
      .channel('orders-changes')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'orders' },
        () => {
          console.log('Orders data changed, refetching customers...');
          fetchCustomers();
        }
      )
      .subscribe();

    return () => {
      userProfilesSubscription.unsubscribe();
      ordersSubscription.unsubscribe();
    };
  }, [isAuthenticated, isAdmin, fetchCustomers]);

  const toggleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const handleViewCustomer = (customer: Customer) => {
    // In a real app, navigate to customer details
    setSelectedCustomer(customer);
  };

  const openOrderHistory = async (customer: Customer) => {
    setSelectedCustomer(customer);
    setIsOrderHistoryOpen(true);
    setIsLoadingOrders(true);

    try {
      const orders = await getCustomerOrders(customer.id);
      setCustomerOrders(orders);
    } catch (error) {
      console.error('Error fetching customer orders:', error);
      toast({
        title: 'Error',
        description: 'Failed to load order history. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsLoadingOrders(false);
    }
  };

  // Set up real-time subscription for customer orders when dialog is open
  useEffect(() => {
    if (!isOrderHistoryOpen || !selectedCustomer) return;

    console.log('Setting up real-time subscription for customer orders');

    // Set up real-time subscription to order changes for this customer
    const orderSubscription = supabase
      .channel(`customer-orders-${selectedCustomer.id}`)
      .on('postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'orders',
          filter: `user_id=eq.${selectedCustomer.id}`
        },
        async () => {
          console.log('Customer orders changed, refetching...');
          const updatedOrders = await getCustomerOrders(selectedCustomer.id);
          setCustomerOrders(updatedOrders);
        }
      )
      .subscribe();

    // Set up real-time subscription to order item changes
    const orderItemsSubscription = supabase
      .channel(`customer-order-items-${selectedCustomer.id}`)
      .on('postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'order_items'
        },
        async () => {
          console.log('Order items changed, refetching customer orders...');
          const updatedOrders = await getCustomerOrders(selectedCustomer.id);
          setCustomerOrders(updatedOrders);
        }
      )
      .subscribe();

    // Clean up subscriptions when dialog closes
    return () => {
      console.log('Cleaning up order subscriptions');
      orderSubscription.unsubscribe();
      orderItemsSubscription.unsubscribe();
    };
  }, [isOrderHistoryOpen, selectedCustomer]);

  // Filter and sort customers
  const filteredCustomers = customers.filter(customer => {
    return (
      customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      customer.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (customer.phone && customer.phone.includes(searchQuery))
    );
  }).sort((a, b) => {
    if (sortField === 'orders_count') {
      const aCount = a.orders_count || 0;
      const bCount = b.orders_count || 0;
      return sortDirection === 'asc' ? aCount - bCount : bCount - aCount;
    } else if (sortField === 'created_at') {
      return sortDirection === 'asc'
        ? new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        : new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
    }
    // Default string comparison for other fields
    return sortDirection === 'asc'
      ? (a[sortField as keyof Customer] as string)?.localeCompare(b[sortField as keyof Customer] as string)
      : (b[sortField as keyof Customer] as string)?.localeCompare(a[sortField as keyof Customer] as string);
  });

  if (!isAuthenticated || !isAdmin()) {
    return (
      <div className="min-h-screen">
        <Navbar />
        <div className="pt-28 pb-16 flex items-center justify-center">
          <div className="max-w-md w-full p-8 bg-white rounded-lg shadow-md">
            <h1 className="text-2xl font-bold text-center mb-6">Admin Access Required</h1>
            <p className="text-badhees-600 mb-6 text-center">
              You need to be logged in as an admin to access this page.
            </p>
            <div className="flex justify-center">
              <Button asChild className="w-full">
                <a href="/login?redirect=/admin/customers">Login</a>
              </Button>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex">
      <AdminSidebar />

      <div className="flex-1">
        <Navbar />

        <div className="pt-28 pb-16 px-4 sm:px-8 max-w-[1400px] mx-auto">
          <h1 className="text-2xl md:text-3xl font-bold text-badhees-800 mb-6">
            Customer Management
          </h1>

          {/* Search and Refresh */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
            <div className="relative w-full max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-badhees-400" />
              <Input
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search by name, email, or phone"
                className="pl-10"
              />
            </div>
            <Button
              variant="outline"
              onClick={fetchCustomers}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Refresh Data
            </Button>
          </div>

          {/* Customer Stats */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
            <div className="bg-white p-4 rounded-lg border shadow-sm">
              <div className="flex items-center gap-3">
                <div className="bg-blue-100 p-2 rounded-full">
                  <User className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-500">Total Customers</p>
                  <p className="text-xl font-semibold">{customers.length}</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-4 rounded-lg border shadow-sm">
              <div className="flex items-center gap-3">
                <div className="bg-green-100 p-2 rounded-full">
                  <ShoppingBag className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-500">Total Orders</p>
                  <p className="text-xl font-semibold">{customers.reduce((sum, customer) => sum + (customer.orders_count || 0), 0)}</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-4 rounded-lg border shadow-sm">
              <div className="flex items-center gap-3">
                <div className="bg-purple-100 p-2 rounded-full">
                  <Calendar className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-500">New This Month</p>
                  <p className="text-xl font-semibold">
                    {customers.filter(c => {
                      const date = new Date(c.created_at);
                      const now = new Date();
                      return date.getMonth() === now.getMonth() && date.getFullYear() === now.getFullYear();
                    }).length}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Customers Table */}
          <div className="rounded-md border overflow-x-auto">
            {isLoading ? (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-badhees-600" />
                <span className="ml-3 text-badhees-600">Loading customers...</span>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[180px]">
                      <button
                        type="button"
                        className="flex items-center"
                        onClick={() => toggleSort('name')}
                      >
                        Customer Name
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      </button>
                    </TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Phone</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>
                      <button
                        type="button"
                        className="flex items-center"
                        onClick={() => toggleSort('orders_count')}
                      >
                        Orders
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      </button>
                    </TableHead>
                    <TableHead>
                      <button
                        type="button"
                        className="flex items-center"
                        onClick={() => toggleSort('created_at')}
                      >
                        Joined
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      </button>
                    </TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredCustomers.length > 0 ? (
                    filteredCustomers.map((customer) => (
                      <TableRow key={customer.id}>
                        <TableCell className="font-medium">{customer.name}</TableCell>
                        <TableCell>{customer.email}</TableCell>
                        <TableCell>{customer.phone || '-'}</TableCell>
                        <TableCell>
                          <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                            customer.status === 'active'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {customer.status === 'active' ? 'Active' : 'Inactive'}
                          </span>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <span className={`font-medium ${customer.orders_count ? 'text-badhees-800' : 'text-gray-400'}`}>
                              {customer.orders_count || 0}
                            </span>
                            {customer.orders_count > 0 && (
                              <Button
                                variant="ghost"
                                size="sm"
                                className="ml-2 h-6 px-2 text-xs"
                                onClick={() => openOrderHistory(customer)}
                              >
                                View
                              </Button>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span>{new Date(customer.created_at).toLocaleDateString()}</span>
                            <span className="text-xs text-gray-500">
                              {new Date(customer.created_at).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => handleViewCustomer(customer)}
                              title="View Customer Details"
                            >
                              <User className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => {
                                window.location.href = `mailto:${customer.email}`;
                              }}
                              title="Send Email"
                            >
                              <Mail className="h-4 w-4" />
                            </Button>
                            {customer.phone && (
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => {
                                  window.location.href = `tel:${customer.phone}`;
                                }}
                                title="Call Customer"
                              >
                                <Phone className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8 text-badhees-600">
                        {searchQuery ? 'No customers found matching your search' : 'No customers found'}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            )}
          </div>

          {/* Order History Dialog */}
          <Dialog open={isOrderHistoryOpen} onOpenChange={setIsOrderHistoryOpen}>
            <DialogContent className="max-w-lg">
              <DialogHeader>
                <DialogTitle>Order History</DialogTitle>
                <DialogDescription>
                  {selectedCustomer && `Viewing orders for ${selectedCustomer.name}`}
                </DialogDescription>
              </DialogHeader>

              {isLoadingOrders ? (
                <div className="flex justify-center items-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin text-badhees-600" />
                  <span className="ml-3 text-badhees-600">Loading orders...</span>
                </div>
              ) : customerOrders.length > 0 ? (
                <div className="space-y-4 max-h-[400px] overflow-y-auto pr-2">
                  {customerOrders.map(order => (
                    <div key={order.id} className="p-4 border rounded-md hover:shadow-md transition-shadow">
                      <div className="flex justify-between items-center">
                        <span className="font-medium">Order #{order.id.substring(0, 8)}</span>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          order.status === 'delivered' ? 'bg-green-100 text-green-800' :
                          order.status === 'shipped' ? 'bg-blue-100 text-blue-800' :
                          order.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                          order.status === 'processing' ? 'bg-purple-100 text-purple-800' :
                          'bg-amber-100 text-amber-800'
                        }`}>
                          {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                        </span>
                      </div>
                      <div className="flex justify-between mt-2 text-sm text-badhees-600">
                        <div className="flex flex-col">
                          <span>{new Date(order.created_at).toLocaleDateString()}</span>
                          <span className="text-xs text-gray-500">{new Date(order.created_at).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</span>
                        </div>
                        <span className="font-medium">₹{order.total_amount?.toLocaleString('en-IN', { maximumFractionDigits: 2 }) || 'N/A'}</span>
                      </div>
                      <div className="mt-2 text-sm">
                        <span className="text-badhees-600">Payment: </span>
                        <span className="font-medium">{order.payment_method || 'Not specified'}</span>
                      </div>
                      {order.order_items && order.order_items.length > 0 && (
                        <div className="mt-3 pt-3 border-t text-sm">
                          <div className="text-badhees-600 mb-1 font-medium">Items: {order.order_items.length}</div>
                          {order.order_items.map((item: any, index: number) => (
                            <div key={index} className="flex justify-between py-1 border-b border-gray-100 last:border-0">
                              <div className="text-badhees-700">
                                {item.product?.name || 'Product'} <span className="text-gray-500">x {item.quantity}</span>
                              </div>
                              <div className="text-badhees-700">₹{(item.price * item.quantity).toLocaleString('en-IN', { maximumFractionDigits: 2 })}</div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="py-8 text-center text-badhees-600">
                  No orders found for this customer.
                </div>
              )}

              <DialogFooter>
                <Button onClick={() => setIsOrderHistoryOpen(false)}>Close</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        <Footer />
      </div>
    </div>
  );
};

export default AdminCustomers;
