-- ===========================================
-- THE BADHEES - COMPLETE PERFORMANCE OPTIMIZATION
-- ===========================================
-- This file contains all database performance optimizations for The Badhees website
-- Execute these queries to optimize database performance

-- ===========================================
-- 1. CREATE PERFORMANCE INDEXES
-- ===========================================

-- Products table indexes
CREATE INDEX IF NOT EXISTS idx_products_category_active ON products(category_id, is_active);
CREATE INDEX IF NOT EXISTS idx_products_featured_active ON products(is_featured, is_active);
CREATE INDEX IF NOT EXISTS idx_products_sale ON products(is_sale, is_active);
CREATE INDEX IF NOT EXISTS idx_products_price ON products(price);
CREATE INDEX IF NOT EXISTS idx_products_name_search ON products USING gin(to_tsvector('english', name));
CREATE INDEX IF NOT EXISTS idx_products_description_search ON products USING gin(to_tsvector('english', description));
CREATE INDEX IF NOT EXISTS idx_products_created_at ON products(created_at DESC);

-- Product images indexes
CREATE INDEX IF NOT EXISTS idx_product_images_product_primary ON product_images(product_id, is_primary);
CREATE INDEX IF NOT EXISTS idx_product_images_display_order ON product_images(product_id, display_order);

-- Orders table indexes
CREATE INDEX IF NOT EXISTS idx_orders_user_status ON orders(user_id, status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_orders_payment_status ON orders(payment_status);
CREATE INDEX IF NOT EXISTS idx_orders_razorpay_payment ON orders(razorpay_payment_id);
CREATE INDEX IF NOT EXISTS idx_orders_total_amount ON orders(total_amount);

-- Order items indexes
CREATE INDEX IF NOT EXISTS idx_order_items_order_product ON order_items(order_id, product_id);
CREATE INDEX IF NOT EXISTS idx_order_items_product ON order_items(product_id);

-- Shopping cart indexes
CREATE INDEX IF NOT EXISTS idx_shopping_cart_user_product ON shopping_cart(user_id, product_id);
CREATE INDEX IF NOT EXISTS idx_shopping_cart_updated_at ON shopping_cart(updated_at DESC);

-- Product ratings indexes
CREATE INDEX IF NOT EXISTS idx_product_ratings_product_rating ON product_ratings(product_id, rating);
CREATE INDEX IF NOT EXISTS idx_product_ratings_user ON product_ratings(user_id);
CREATE INDEX IF NOT EXISTS idx_product_ratings_created_at ON product_ratings(created_at DESC);

-- User profiles indexes
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON user_profiles(email);
CREATE INDEX IF NOT EXISTS idx_user_profiles_role ON user_profiles(role);
CREATE INDEX IF NOT EXISTS idx_user_profiles_created_at ON user_profiles(created_at DESC);

-- Contact submissions indexes
CREATE INDEX IF NOT EXISTS idx_contact_submissions_status ON contact_submissions(status);
CREATE INDEX IF NOT EXISTS idx_contact_submissions_created_at ON contact_submissions(created_at DESC);

-- Customization requests indexes
CREATE INDEX IF NOT EXISTS idx_customization_requests_user ON customization_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_customization_requests_status ON customization_requests(status);
CREATE INDEX IF NOT EXISTS idx_customization_requests_created_at ON customization_requests(created_at DESC);

-- Consultation requests indexes
CREATE INDEX IF NOT EXISTS idx_consultation_requests_user ON consultation_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_consultation_requests_status ON consultation_requests(status);
CREATE INDEX IF NOT EXISTS idx_consultation_requests_date ON consultation_requests(preferred_date);

-- Completed projects indexes
CREATE INDEX IF NOT EXISTS idx_completed_projects_featured ON completed_projects(is_featured);
CREATE INDEX IF NOT EXISTS idx_completed_projects_category ON completed_projects(category);
CREATE INDEX IF NOT EXISTS idx_completed_projects_completion_date ON completed_projects(completion_date DESC);

-- Project images indexes
CREATE INDEX IF NOT EXISTS idx_project_images_project_primary ON project_images(project_id, is_primary);
CREATE INDEX IF NOT EXISTS idx_project_images_display_order ON project_images(project_id, display_order);

-- Razorpay payments indexes
CREATE INDEX IF NOT EXISTS idx_razorpay_payments_order_id ON razorpay_payments(order_id);
CREATE INDEX IF NOT EXISTS idx_razorpay_payments_user ON razorpay_payments(user_id);
CREATE INDEX IF NOT EXISTS idx_razorpay_payments_status ON razorpay_payments(status);
CREATE INDEX IF NOT EXISTS idx_razorpay_payments_created_at ON razorpay_payments(created_at DESC);

-- ===========================================
-- 2. CREATE COMPOSITE INDEXES FOR COMPLEX QUERIES
-- ===========================================

-- Products with category and status
CREATE INDEX IF NOT EXISTS idx_products_complex_search ON products(category_id, is_active, is_featured, price);

-- Orders with user and date range
CREATE INDEX IF NOT EXISTS idx_orders_user_date_status ON orders(user_id, created_at DESC, status);

-- Product ratings aggregation
CREATE INDEX IF NOT EXISTS idx_product_ratings_aggregation ON product_ratings(product_id, rating, created_at);

-- ===========================================
-- 3. CREATE MATERIALIZED VIEWS FOR ANALYTICS
-- ===========================================

-- Product statistics view
CREATE MATERIALIZED VIEW IF NOT EXISTS product_stats AS
SELECT 
    p.id,
    p.name,
    p.category_id,
    c.name as category_name,
    p.price,
    p.sale_price,
    p.is_sale,
    p.is_featured,
    p.stock_quantity,
    COALESCE(AVG(pr.rating), 0) as avg_rating,
    COUNT(pr.rating) as rating_count,
    COUNT(oi.id) as total_orders,
    SUM(oi.quantity) as total_sold,
    p.created_at
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
LEFT JOIN product_ratings pr ON p.id = pr.product_id
LEFT JOIN order_items oi ON p.id = oi.product_id
WHERE p.is_active = true
GROUP BY p.id, c.name;

-- Create index on materialized view
CREATE INDEX IF NOT EXISTS idx_product_stats_category ON product_stats(category_id);
CREATE INDEX IF NOT EXISTS idx_product_stats_rating ON product_stats(avg_rating DESC);
CREATE INDEX IF NOT EXISTS idx_product_stats_sales ON product_stats(total_sold DESC);

-- Order statistics view
CREATE MATERIALIZED VIEW IF NOT EXISTS order_stats AS
SELECT 
    DATE_TRUNC('day', created_at) as order_date,
    COUNT(*) as total_orders,
    SUM(total_amount) as total_revenue,
    AVG(total_amount) as avg_order_value,
    COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered_orders,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders,
    COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing_orders,
    COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) as paid_orders
FROM orders
GROUP BY DATE_TRUNC('day', created_at)
ORDER BY order_date DESC;

-- Create index on order stats
CREATE INDEX IF NOT EXISTS idx_order_stats_date ON order_stats(order_date DESC);

-- ===========================================
-- 4. CREATE FUNCTIONS FOR COMMON QUERIES
-- ===========================================

-- Function to get product with ratings
CREATE OR REPLACE FUNCTION get_product_with_stats(product_uuid UUID)
RETURNS TABLE (
    id UUID,
    name VARCHAR,
    description TEXT,
    price DECIMAL,
    sale_price DECIMAL,
    is_sale BOOLEAN,
    category_name VARCHAR,
    avg_rating DECIMAL,
    rating_count BIGINT,
    stock_quantity INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.name,
        p.description,
        p.price,
        p.sale_price,
        p.is_sale,
        c.name as category_name,
        COALESCE(AVG(pr.rating), 0)::DECIMAL as avg_rating,
        COUNT(pr.rating) as rating_count,
        p.stock_quantity
    FROM products p
    LEFT JOIN categories c ON p.category_id = c.id
    LEFT JOIN product_ratings pr ON p.id = pr.product_id
    WHERE p.id = product_uuid AND p.is_active = true
    GROUP BY p.id, c.name;
END;
$$ LANGUAGE plpgsql;

-- Function to get user order statistics
CREATE OR REPLACE FUNCTION get_user_order_stats(user_uuid UUID)
RETURNS TABLE (
    total_orders BIGINT,
    total_spent DECIMAL,
    avg_order_value DECIMAL,
    last_order_date TIMESTAMP
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_orders,
        COALESCE(SUM(total_amount), 0) as total_spent,
        COALESCE(AVG(total_amount), 0) as avg_order_value,
        MAX(created_at) as last_order_date
    FROM orders
    WHERE user_id = user_uuid;
END;
$$ LANGUAGE plpgsql;

-- Function to get order statistics for admin dashboard
CREATE OR REPLACE FUNCTION get_order_stats()
RETURNS TABLE (
    total_orders BIGINT,
    pending_orders BIGINT,
    processing_orders BIGINT,
    delivered_orders BIGINT,
    total_revenue DECIMAL,
    avg_order_value DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_orders,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders,
        COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing_orders,
        COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered_orders,
        COALESCE(SUM(total_amount), 0) as total_revenue,
        COALESCE(AVG(total_amount), 0) as avg_order_value
    FROM orders;
END;
$$ LANGUAGE plpgsql;

-- ===========================================
-- 5. CREATE TRIGGERS FOR AUTOMATIC UPDATES
-- ===========================================

-- Function to update materialized views
CREATE OR REPLACE FUNCTION refresh_materialized_views()
RETURNS TRIGGER AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY product_stats;
    REFRESH MATERIALIZED VIEW CONCURRENTLY order_stats;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Triggers to refresh materialized views
CREATE TRIGGER refresh_product_stats_trigger
    AFTER INSERT OR UPDATE OR DELETE ON products
    FOR EACH STATEMENT
    EXECUTE FUNCTION refresh_materialized_views();

CREATE TRIGGER refresh_order_stats_trigger
    AFTER INSERT OR UPDATE OR DELETE ON orders
    FOR EACH STATEMENT
    EXECUTE FUNCTION refresh_materialized_views();

-- ===========================================
-- 6. VACUUM AND ANALYZE FOR OPTIMIZATION
-- ===========================================

-- Analyze all tables to update statistics
ANALYZE products;
ANALYZE categories;
ANALYZE orders;
ANALYZE order_items;
ANALYZE shopping_cart;
ANALYZE product_ratings;
ANALYZE user_profiles;
ANALYZE contact_submissions;
ANALYZE customization_requests;
ANALYZE consultation_requests;
ANALYZE completed_projects;
ANALYZE project_images;
ANALYZE razorpay_payments;

-- ===========================================
-- 7. PERFORMANCE MONITORING QUERIES
-- ===========================================

-- Query to check index usage
-- SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
-- FROM pg_stat_user_indexes
-- ORDER BY idx_scan DESC;

-- Query to check table sizes
-- SELECT schemaname, tablename, pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
-- FROM pg_tables
-- WHERE schemaname = 'public'
-- ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- ===========================================
-- 8. OPTIMIZATION COMPLETE
-- ===========================================
-- Performance optimization is now complete!
-- 
-- Regular maintenance tasks:
-- 1. REFRESH MATERIALIZED VIEW product_stats;
-- 2. REFRESH MATERIALIZED VIEW order_stats;
-- 3. VACUUM ANALYZE; (run weekly)
-- 4. Monitor slow queries and add indexes as needed
