// =====================================================
// TEST MOBILE PAYMENT RECOVERY TRIGGER
// =====================================================
// Run this in browser console to manually trigger mobile payment recovery

async function testMobileRecoveryTrigger() {
  console.log('🚀 TESTING MOBILE PAYMENT RECOVERY TRIGGER');
  console.log('==========================================');
  
  try {
    // Check if there's stored mobile payment data
    const storedData = localStorage.getItem('mobile_payment_data');
    
    if (!storedData) {
      console.log('❌ No mobile payment data found in localStorage');
      console.log('💡 Please make a payment first to generate test data');
      return;
    }
    
    console.log('✅ Found stored mobile payment data');
    
    // Parse the data
    const paymentData = JSON.parse(storedData);
    console.log('📋 Payment data:', paymentData);
    
    // Check if data is valid (not too old)
    const maxAge = 30 * 60 * 1000; // 30 minutes
    const dataAge = Date.now() - paymentData.timestamp;
    const isValid = dataAge < maxAge;
    
    console.log(`⏰ Data age: ${Math.floor(dataAge / (1000 * 60))} minutes`);
    console.log(`✅ Data valid: ${isValid ? 'YES' : 'NO'}`);
    
    if (!isValid) {
      console.log('❌ Data is too old for recovery');
      return;
    }
    
    // Check if we're on mobile
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    console.log(`📱 Is mobile device: ${isMobile ? 'YES' : 'NO'}`);
    
    // Check current URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const paymentId = urlParams.get('razorpay_payment_id');
    const orderId = urlParams.get('razorpay_order_id');
    const signature = urlParams.get('razorpay_signature');
    
    console.log('\n🔗 URL PARAMETERS:');
    console.log('Payment ID:', paymentId || 'Not found');
    console.log('Order ID:', orderId || 'Not found');
    console.log('Signature:', signature || 'Not found');
    
    // Simulate what the MobilePaymentRecovery component should do
    console.log('\n🔄 SIMULATING RECOVERY PROCESS:');
    
    if (paymentId && orderId && signature) {
      console.log('✅ Full payment parameters found - would trigger normal recovery');
    } else if (paymentId && orderId) {
      console.log('⚠️ Partial payment parameters found - would trigger recovery without signature');
    } else {
      console.log('❌ No payment parameters found - would trigger mobile UPI recovery');
      console.log('📱 This is the scenario we need to handle!');
      
      // This is where our fix should kick in
      if (paymentData.razorpayOrderId) {
        console.log('✅ Found Razorpay Order ID in stored data:', paymentData.razorpayOrderId);
        console.log('🔄 Would attempt mobile UPI recovery with stored data');
        
        // Show what data would be used for recovery
        console.log('\n📦 RECOVERY DATA:');
        console.log('User ID:', paymentData.userId);
        console.log('Amount:', paymentData.amount);
        console.log('Cart Items:', paymentData.cartItems?.length || 0);
        console.log('Shipping Address:', paymentData.shippingAddress ? 'Available' : 'Missing');
        console.log('Billing Address:', paymentData.billingAddress ? 'Available' : 'Missing');
        console.log('Razorpay Order ID:', paymentData.razorpayOrderId);
        
        // Check if address data is valid
        if (paymentData.shippingAddress) {
          const addressStr = JSON.stringify(paymentData.shippingAddress);
          if (addressStr === '{"test":true}') {
            console.log('❌ PROBLEM: Shipping address contains dummy test data!');
            console.log('🔧 This would cause the validation to fail');
          } else {
            console.log('✅ Shipping address looks valid');
          }
        }
        
        if (paymentData.billingAddress) {
          const addressStr = JSON.stringify(paymentData.billingAddress);
          if (addressStr === '{"test":true}') {
            console.log('❌ PROBLEM: Billing address contains dummy test data!');
            console.log('🔧 This would cause the validation to fail');
          } else {
            console.log('✅ Billing address looks valid');
          }
        }
        
      } else {
        console.log('❌ No Razorpay Order ID found in stored data');
      }
    }
    
    // Check if MobilePaymentRecovery component is loaded
    console.log('\n🔍 CHECKING COMPONENT STATUS:');
    
    // Look for the recovery component in the DOM
    const recoveryElement = document.querySelector('[class*="fixed"][class*="inset-0"]');
    if (recoveryElement) {
      console.log('✅ Found potential recovery component in DOM');
    } else {
      console.log('❌ No recovery component visible in DOM');
    }
    
    // Check if we can manually trigger the recovery
    console.log('\n🎯 MANUAL TRIGGER ATTEMPT:');
    console.log('To manually trigger recovery, the MobilePaymentRecovery component should:');
    console.log('1. Detect mobile device ✓');
    console.log('2. Find stored payment data ✓');
    console.log('3. Check data validity ✓');
    console.log('4. Attempt recovery even without URL parameters ← NEW FIX');
    
    // Provide next steps
    console.log('\n📋 NEXT STEPS:');
    console.log('1. Refresh the page to trigger MobilePaymentRecovery component');
    console.log('2. Check browser console for recovery logs');
    console.log('3. Look for recovery modal/popup');
    console.log('4. If recovery fails, check the error messages');
    
    console.log('\n🎉 Mobile recovery trigger test completed!');
    
  } catch (error) {
    console.error('❌ Mobile recovery trigger test failed:', error);
  }
}

// Auto-run the test
testMobileRecoveryTrigger();

// Also provide a manual trigger function
window.testMobileRecovery = testMobileRecoveryTrigger;
