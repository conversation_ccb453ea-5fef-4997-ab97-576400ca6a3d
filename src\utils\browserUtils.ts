/**
 * Browser Utilities
 * 
 * Utilities for safely handling browser APIs and preventing console errors
 */

/**
 * Safely check if a browser API is available
 * 
 * @param api - The API to check (e.g., 'vibrate', 'share', 'clipboard')
 * @returns Boolean indicating if the API is available
 */
export const isBrowserAPISupported = (api: string): boolean => {
  if (typeof window === 'undefined' || typeof navigator === 'undefined') {
    return false;
  }

  try {
    switch (api) {
      case 'vibrate':
        return 'vibrate' in navigator;
      case 'share':
        return 'share' in navigator;
      case 'clipboard':
        return 'clipboard' in navigator;
      case 'geolocation':
        return 'geolocation' in navigator;
      case 'serviceWorker':
        return 'serviceWorker' in navigator;
      default:
        return api in navigator;
    }
  } catch (error) {
    console.debug(`Error checking browser API support for ${api}:`, error);
    return false;
  }
};

/**
 * Safely execute a browser API call with error handling
 * 
 * @param apiCall - Function that makes the API call
 * @param fallback - Optional fallback function if API fails
 * @returns Promise that resolves with the result or undefined if failed
 */
export const safeBrowserAPICall = async <T>(
  apiCall: () => Promise<T> | T,
  fallback?: () => void
): Promise<T | undefined> => {
  try {
    return await apiCall();
  } catch (error) {
    console.debug('Browser API call failed:', error);
    if (fallback) {
      fallback();
    }
    return undefined;
  }
};

/**
 * Safely trigger device vibration
 * 
 * @param pattern - Vibration pattern (number or array of numbers)
 */
export const safeVibrate = (pattern: number | number[]): void => {
  if (!isBrowserAPISupported('vibrate')) {
    return;
  }

  safeBrowserAPICall(() => {
    navigator.vibrate(pattern);
  });
};

/**
 * Safely copy text to clipboard
 * 
 * @param text - Text to copy
 * @returns Promise that resolves to boolean indicating success
 */
export const safeCopyToClipboard = async (text: string): Promise<boolean> => {
  if (!isBrowserAPISupported('clipboard')) {
    // Fallback to older method
    try {
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      const result = document.execCommand('copy');
      document.body.removeChild(textArea);
      return result;
    } catch (error) {
      console.debug('Fallback clipboard copy failed:', error);
      return false;
    }
  }

  const result = await safeBrowserAPICall(async () => {
    await navigator.clipboard.writeText(text);
    return true;
  });

  return result ?? false;
};

/**
 * Safely share content using Web Share API
 * 
 * @param shareData - Data to share
 * @returns Promise that resolves to boolean indicating success
 */
export const safeShare = async (shareData: ShareData): Promise<boolean> => {
  if (!isBrowserAPISupported('share')) {
    return false;
  }

  const result = await safeBrowserAPICall(async () => {
    await navigator.share(shareData);
    return true;
  });

  return result ?? false;
};

/**
 * Prevent console errors by wrapping potentially problematic operations
 * 
 * @param operation - Function to execute safely
 * @param errorMessage - Optional custom error message
 */
export const suppressConsoleErrors = <T>(
  operation: () => T,
  errorMessage?: string
): T | undefined => {
  try {
    return operation();
  } catch (error) {
    if (errorMessage) {
      console.debug(errorMessage, error);
    }
    return undefined;
  }
};

/**
 * Initialize browser compatibility checks and suppress common errors
 */
export const initBrowserCompatibility = (): void => {
  // Suppress vibration errors
  if (typeof navigator !== 'undefined' && !isBrowserAPISupported('vibrate')) {
    // Override navigator.vibrate to prevent errors
    Object.defineProperty(navigator, 'vibrate', {
      value: () => false,
      writable: false,
      configurable: false
    });
  }

  // Suppress other common browser API errors
  const originalError = console.error;
  console.error = (...args) => {
    const message = args.join(' ');
    
    // Filter out known harmless errors
    if (
      message.includes('navigator.vibrate') ||
      message.includes('Blocked call to navigator.vibrate') ||
      message.includes('cross-origin iframe') ||
      message.includes('Expected length, "auto"') ||
      message.includes('chrome-extension://') ||
      message.includes('Extension context invalidated') ||
      message.includes('Failed to load resource: the server responded with a status of 404') ||
      message.includes('chrome_error') ||
      message.includes('chrome.runtime') ||
      message.includes('moz-extension://') ||
      message.includes('safari-extension://')
    ) {
      return; // Suppress these specific errors
    }
    
    // Log other errors normally
    originalError.apply(console, args);
  };
};
