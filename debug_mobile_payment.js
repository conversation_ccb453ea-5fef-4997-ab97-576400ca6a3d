/**
 * Mobile Payment Debug Script
 * Run this in browser console to debug mobile payment issues
 */

// Debug Mobile Payment Flow
const debugMobilePayment = {
  
  // Check session status
  async checkSession() {
    console.log('🔍 [Debug] Checking user session...');
    
    try {
      const { data: { session }, error } = await window.supabase.auth.getSession();
      
      if (error) {
        console.error('❌ [Debug] Session error:', error);
        return { valid: false, error: error.message };
      }
      
      if (!session || !session.user) {
        console.error('❌ [Debug] No valid session found');
        return { valid: false, error: 'No session' };
      }
      
      console.log('✅ [Debug] Valid session found:', {
        userId: session.user.id,
        email: session.user.email,
        expiresAt: session.expires_at
      });
      
      return { valid: true, session };
    } catch (err) {
      console.error('❌ [Debug] Session check failed:', err);
      return { valid: false, error: err.message };
    }
  },
  
  // Test database connection
  async testDatabaseConnection() {
    console.log('🔍 [Debug] Testing database connection...');
    
    try {
      const { data, error } = await window.supabase
        .from('orders')
        .select('id')
        .limit(1);
      
      if (error) {
        console.error('❌ [Debug] Database connection error:', error);
        return { connected: false, error: error.message };
      }
      
      console.log('✅ [Debug] Database connection successful');
      return { connected: true };
    } catch (err) {
      console.error('❌ [Debug] Database connection failed:', err);
      return { connected: false, error: err.message };
    }
  },
  
  // Test RLS policies
  async testRLSPolicies() {
    console.log('🔍 [Debug] Testing RLS policies...');
    
    const sessionCheck = await this.checkSession();
    if (!sessionCheck.valid) {
      return { error: 'No valid session for RLS test' };
    }
    
    const userId = sessionCheck.session.user.id;
    
    try {
      // Test orders table insert
      console.log('🔍 [Debug] Testing orders table RLS...');
      const testOrderData = {
        user_id: userId,
        status: 'pending',
        payment_method: 'test',
        payment_status: 'pending',
        total_amount: 1.00,
        shipping_address: { test: true },
        billing_address: { test: true }
      };
      
      const { data: orderData, error: orderError } = await window.supabase
        .from('orders')
        .insert([testOrderData])
        .select()
        .single();
      
      if (orderError) {
        console.error('❌ [Debug] Orders RLS test failed:', orderError);
        return { ordersRLS: false, error: orderError.message };
      }
      
      console.log('✅ [Debug] Orders RLS test passed, order created:', orderData.id);
      
      // Test order_items table insert
      console.log('🔍 [Debug] Testing order_items table RLS...');
      const testItemData = {
        order_id: orderData.id,
        product_id: '00000000-0000-0000-0000-000000000000', // Dummy UUID
        quantity: 1,
        price: 1.00,
        total: 1.00
      };
      
      const { data: itemData, error: itemError } = await window.supabase
        .from('order_items')
        .insert([testItemData]);
      
      if (itemError) {
        console.error('❌ [Debug] Order items RLS test failed:', itemError);
        
        // Clean up test order
        await window.supabase.from('orders').delete().eq('id', orderData.id);
        
        return { ordersRLS: true, orderItemsRLS: false, error: itemError.message };
      }
      
      console.log('✅ [Debug] Order items RLS test passed');
      
      // Clean up test data
      await window.supabase.from('order_items').delete().eq('order_id', orderData.id);
      await window.supabase.from('orders').delete().eq('id', orderData.id);
      
      console.log('✅ [Debug] Test data cleaned up');
      
      return { ordersRLS: true, orderItemsRLS: true };
      
    } catch (err) {
      console.error('❌ [Debug] RLS test failed:', err);
      return { error: err.message };
    }
  },
  
  // Check localStorage data
  checkLocalStorageData() {
    console.log('🔍 [Debug] Checking localStorage data...');
    
    const mobilePaymentData = localStorage.getItem('mobile_payment_data');
    const failedOrderData = localStorage.getItem('failed_order_data');
    
    console.log('📱 Mobile payment data:', mobilePaymentData ? JSON.parse(mobilePaymentData) : 'None');
    console.log('💥 Failed order data:', failedOrderData ? JSON.parse(failedOrderData) : 'None');
    
    return {
      mobilePaymentData: mobilePaymentData ? JSON.parse(mobilePaymentData) : null,
      failedOrderData: failedOrderData ? JSON.parse(failedOrderData) : null
    };
  },
  
  // Check URL parameters
  checkURLParameters() {
    console.log('🔍 [Debug] Checking URL parameters...');
    
    const urlParams = new URLSearchParams(window.location.search);
    const params = {};
    
    for (const [key, value] of urlParams.entries()) {
      params[key] = value;
    }
    
    console.log('🔗 URL parameters:', params);
    return params;
  },
  
  // Run full diagnostic
  async runFullDiagnostic() {
    console.log('🚀 [Debug] Starting full mobile payment diagnostic...');
    console.log('=' .repeat(60));
    
    const results = {};
    
    // 1. Check session
    console.log('\n1. SESSION CHECK');
    console.log('-'.repeat(30));
    results.session = await this.checkSession();
    
    // 2. Test database connection
    console.log('\n2. DATABASE CONNECTION');
    console.log('-'.repeat(30));
    results.database = await this.testDatabaseConnection();
    
    // 3. Test RLS policies
    console.log('\n3. RLS POLICIES TEST');
    console.log('-'.repeat(30));
    results.rls = await this.testRLSPolicies();
    
    // 4. Check localStorage
    console.log('\n4. LOCALSTORAGE DATA');
    console.log('-'.repeat(30));
    results.localStorage = this.checkLocalStorageData();
    
    // 5. Check URL parameters
    console.log('\n5. URL PARAMETERS');
    console.log('-'.repeat(30));
    results.urlParams = this.checkURLParameters();
    
    // Summary
    console.log('\n📊 DIAGNOSTIC SUMMARY');
    console.log('=' .repeat(60));
    console.log('Session Valid:', results.session.valid ? '✅' : '❌');
    console.log('Database Connected:', results.database.connected ? '✅' : '❌');
    console.log('Orders RLS:', results.rls.ordersRLS ? '✅' : '❌');
    console.log('Order Items RLS:', results.rls.orderItemsRLS ? '✅' : '❌');
    console.log('Mobile Payment Data:', results.localStorage.mobilePaymentData ? '✅' : '❌');
    console.log('Failed Order Data:', results.localStorage.failedOrderData ? '✅' : '❌');
    
    if (!results.session.valid) {
      console.log('\n🔥 CRITICAL: Session is invalid - this is likely the root cause!');
    }
    
    if (!results.database.connected) {
      console.log('\n🔥 CRITICAL: Database connection failed!');
    }
    
    if (!results.rls.ordersRLS || !results.rls.orderItemsRLS) {
      console.log('\n🔥 CRITICAL: RLS policies are blocking database inserts!');
    }
    
    return results;
  }
};

// Make it globally available
window.debugMobilePayment = debugMobilePayment;

console.log('🔧 Mobile Payment Debug Script Loaded!');
console.log('Run: debugMobilePayment.runFullDiagnostic() to start debugging');
console.log('Or run individual tests:');
console.log('- debugMobilePayment.checkSession()');
console.log('- debugMobilePayment.testDatabaseConnection()');
console.log('- debugMobilePayment.testRLSPolicies()');
console.log('- debugMobilePayment.checkLocalStorageData()');
console.log('- debugMobilePayment.checkURLParameters()');
