import { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate, Link } from 'react-router-dom';
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, ArrowLeft, Truck, Package, CheckCircle, XCircle, Clock, AlertCircle } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { useAuth } from '@/context/SupabaseAuthContext';
import { Order, getOrderById, updateOrderStatus } from '@/services/orderService';
import { Dialog, DialogContent, DialogDes<PERSON>, Di<PERSON>Footer, Di<PERSON>Header, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';

// Status colors for badges
const statusColors = {
  pending: 'bg-amber-100 text-amber-800 hover:bg-amber-200',
  processing: 'bg-purple-100 text-purple-800 hover:bg-purple-200',
  shipped: 'bg-blue-100 text-blue-800 hover:bg-blue-200',
  delivered: 'bg-green-100 text-green-800 hover:bg-green-200',
  cancelled: 'bg-red-100 text-red-800 hover:bg-red-200'
};

// Status icons
const statusIcons = {
  pending: <Clock className="h-5 w-5 mr-2" />,
  processing: <Package className="h-5 w-5 mr-2" />,
  shipped: <Truck className="h-5 w-5 mr-2" />,
  delivered: <CheckCircle className="h-5 w-5 mr-2" />,
  cancelled: <XCircle className="h-5 w-5 mr-2" />
};

const AdminOrderDetails = () => {
  const { orderId } = useParams<{ orderId: string }>();
  const navigate = useNavigate();
  const { isAuthenticated, isAdmin } = useAuth();
  
  const [order, setOrder] = useState<Order | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);
  const [newStatus, setNewStatus] = useState<'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled'>('pending');
  const [statusNote, setStatusNote] = useState('');

  // Fetch order details
  useEffect(() => {
    const fetchOrderDetails = async () => {
      if (!orderId) return;
      
      setIsLoading(true);
      try {
        const orderData = await getOrderById(orderId);
        if (orderData) {
          setOrder(orderData);
          setNewStatus(orderData.status);
        } else {
          toast({
            title: 'Order not found',
            description: 'The requested order could not be found.',
            variant: 'destructive'
          });
          navigate('/admin/orders');
        }
      } catch (error) {
        console.error('Error fetching order details:', error);
        toast({
          title: 'Error',
          description: 'Failed to load order details. Please try again.',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    };

    // Check if user has access to this page
    if (!isAuthenticated) {
      navigate('/login?redirect=/admin/orders');
    } else if (!isAdmin()) {
      navigate('/');
    } else {
      fetchOrderDetails();
    }
  }, [orderId, isAuthenticated, isAdmin, navigate]);

  // Handle status update
  const handleUpdateStatus = async () => {
    if (!order) return;
    
    try {
      const success = await updateOrderStatus(order.id, newStatus);
      
      if (success) {
        // Update local state
        setOrder({
          ...order,
          status: newStatus,
          updated_at: new Date().toISOString()
        });
        
        // Close dialog
        setIsStatusDialogOpen(false);
        
        // Reset status note
        setStatusNote('');
        
        toast({
          title: 'Status updated',
          description: `Order status has been updated to ${newStatus}.`,
        });
      }
    } catch (error) {
      console.error('Error updating order status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update order status. Please try again.',
        variant: 'destructive'
      });
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="flex-grow container mx-auto px-4 py-8 flex items-center justify-center">
          <div className="flex flex-col items-center">
            <Loader2 className="h-8 w-8 animate-spin text-badhees-600 mb-4" />
            <p className="text-badhees-600">Loading order details...</p>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (!order) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="flex-grow container mx-auto px-4 py-8">
          <div className="text-center py-12 bg-badhees-50 rounded-lg">
            <AlertCircle className="h-12 w-12 mx-auto text-badhees-600 mb-4" />
            <h2 className="text-2xl font-bold text-badhees-800 mb-2">Order Not Found</h2>
            <p className="text-badhees-600 mb-6">The order you're looking for doesn't exist or you don't have permission to view it.</p>
            <Button asChild>
              <Link to="/admin/orders">Back to Orders</Link>
            </Button>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <div className="flex-grow container mx-auto px-4 py-8">
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="sm" className="mr-4" onClick={() => navigate('/admin/orders')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Orders
          </Button>
          <h1 className="text-2xl font-bold">Order Details</h1>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Order Summary */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Order #{order.id.substring(0, 8)}</CardTitle>
                  <CardDescription>
                    Placed on {new Date(order.created_at).toLocaleDateString()} at {new Date(order.created_at).toLocaleTimeString()}
                  </CardDescription>
                </div>
                <Badge className={statusColors[order.status]}>
                  {statusIcons[order.status]}
                  {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Order Items */}
                <div>
                  <h3 className="font-medium text-lg mb-3">Order Items</h3>
                  <div className="space-y-4">
                    {order.order_items?.map((item) => (
                      <div key={item.id} className="flex items-start border-b pb-4">
                        <div className="w-16 h-16 bg-badhees-100 rounded overflow-hidden mr-4 flex-shrink-0">
                          <img 
                            src={item.product?.image_url || '/placeholder-product.jpg'} 
                            alt={item.product?.name || 'Product'} 
                            className="w-full h-full object-cover" 
                          />
                        </div>
                        <div className="flex-grow">
                          <div className="flex justify-between">
                            <h4 className="font-medium">{item.product?.name || 'Product'}</h4>
                            <p className="font-medium">₹{(item.price * item.quantity).toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</p>
                          </div>
                          <div className="flex justify-between text-sm text-badhees-500 mt-1">
                            <p>Quantity: {item.quantity}</p>
                            <p>₹{item.price.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })} each</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Order Summary */}
                <div className="bg-badhees-50 p-4 rounded-lg">
                  <div className="flex justify-between mb-2">
                    <span>Subtotal:</span>
                    <span>₹{order.total_amount.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</span>
                  </div>
                  <div className="flex justify-between mb-2">
                    <span>Shipping:</span>
                    <span>Free</span>
                  </div>
                  <Separator className="my-2" />
                  <div className="flex justify-between font-bold">
                    <span>Total:</span>
                    <span>₹{order.total_amount.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</span>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <div>
                <p className="text-sm text-badhees-500">Payment Method</p>
                <p className="font-medium">{order.payment_method || 'Not specified'}</p>
              </div>
              <Dialog open={isStatusDialogOpen} onOpenChange={setIsStatusDialogOpen}>
                <DialogTrigger asChild>
                  <Button>Update Status</Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Update Order Status</DialogTitle>
                    <DialogDescription>
                      Change the status of order #{order.id.substring(0, 8)}
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4 py-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">New Status</label>
                      <Select value={newStatus} onValueChange={(value) => setNewStatus(value as any)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="pending">Pending</SelectItem>
                          <SelectItem value="processing">Processing</SelectItem>
                          <SelectItem value="shipped">Shipped</SelectItem>
                          <SelectItem value="delivered">Delivered</SelectItem>
                          <SelectItem value="cancelled">Cancelled</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Status Note (Optional)</label>
                      <Textarea 
                        placeholder="Add a note about this status change" 
                        value={statusNote}
                        onChange={(e) => setStatusNote(e.target.value)}
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsStatusDialogOpen(false)}>Cancel</Button>
                    <Button onClick={handleUpdateStatus}>Update Status</Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </CardFooter>
          </Card>

          {/* Customer Information */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Customer Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm font-medium text-badhees-500">Customer Name</h3>
                    <p>{order.customer_name || 'Not available'}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-badhees-500">Customer ID</h3>
                    <p>{order.user_id.substring(0, 8)}...</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-badhees-500">Email</h3>
                    <p>{order.user_profiles?.email || 'Not available'}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Shipping Address</CardTitle>
              </CardHeader>
              <CardContent>
                {order.shipping_address ? (
                  <div className="space-y-1">
                    <p>{order.shipping_address.name}</p>
                    <p>{order.shipping_address.street}</p>
                    <p>
                      {order.shipping_address.city}, {order.shipping_address.state} {order.shipping_address.postal_code}
                    </p>
                    <p>{order.shipping_address.country}</p>
                  </div>
                ) : (
                  <p className="text-badhees-500">No shipping address provided</p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Billing Address</CardTitle>
              </CardHeader>
              <CardContent>
                {order.billing_address ? (
                  <div className="space-y-1">
                    <p>{order.billing_address.name}</p>
                    <p>{order.billing_address.street}</p>
                    <p>
                      {order.billing_address.city}, {order.billing_address.state} {order.billing_address.postal_code}
                    </p>
                    <p>{order.billing_address.country}</p>
                  </div>
                ) : (
                  <p className="text-badhees-500">No billing address provided</p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Payment Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm font-medium text-badhees-500">Payment Method</h3>
                    <p>{order.payment_method || 'Not specified'}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-badhees-500">Payment Status</h3>
                    <Badge className={order.payment_status === 'completed' ? 'bg-green-100 text-green-800' :
                                    order.payment_status === 'failed' ? 'bg-red-100 text-red-800' :
                                    'bg-amber-100 text-amber-800'}>
                      {order.payment_status || 'pending'}
                    </Badge>
                  </div>
                  {order.razorpay_payment_id && (
                    <div>
                      <h3 className="text-sm font-medium text-green-600">Razorpay Payment ID</h3>
                      <p className="font-mono text-sm bg-gray-100 px-2 py-1 rounded break-all">
                        {order.razorpay_payment_id}
                      </p>
                      <p className="text-xs text-badhees-500 mt-1">
                        Use this ID to cross-check payment in Razorpay dashboard
                      </p>
                    </div>
                  )}
                  {order.razorpay_order_id && (
                    <div>
                      <h3 className="text-sm font-medium text-blue-600">Razorpay Order ID</h3>
                      <p className="font-mono text-sm bg-gray-100 px-2 py-1 rounded break-all">
                        {order.razorpay_order_id}
                      </p>
                    </div>
                  )}
                  {!order.razorpay_payment_id && !order.razorpay_order_id && order.payment_method !== 'cod' && (
                    <div className="text-amber-600 text-sm">
                      <AlertCircle className="h-4 w-4 inline mr-1" />
                      No payment IDs available
                    </div>
                  )}
                  {order.payment_method === 'cod' && (
                    <div className="text-blue-600 text-sm">
                      <Package className="h-4 w-4 inline mr-1" />
                      Cash on Delivery order
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default AdminOrderDetails;
