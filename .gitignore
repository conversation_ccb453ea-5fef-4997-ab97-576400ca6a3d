# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Environment variables
.env
.env.local
.env.production
.env.development
.env.*.local

# Supabase
supabase/.env.local
supabase/.temp/

# Security
*.pem
*.key
*.crt

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Documentation and temporary files
*.md
*.sql
# Keep essential reference files
!COMPLETE_DATABASE_SETUP.sql
!COMPLETE_FIXES_SUMMARY.md
!COMPLETE_PERFORMANCE_OPTIMIZATION.sql
!COMPLETE_REVIEWS_SYSTEM_SETUP.sql
!COMPLETE_VIBES_SYSTEM_SETUP.sql

# Platform-specific files
.vercel

# Backup files
*.backup
*.bak
*.tmp

# Temporary testing files (local only)
server-temp.cjs
server.cjs
simple-server.js
local-server.js
local-server.cjs
test-*.js
test-*.cjs
*-temp.*
MOBILE_TESTING_GUIDE.md
