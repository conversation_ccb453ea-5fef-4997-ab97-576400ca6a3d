/**
 * Razorpay Payment Verification API
 * Verifies payment signature and updates order status
 */

import crypto from 'crypto';
import { createClient } from '@supabase/supabase-js';

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  try {
    console.log('🔐 [Verify Payment] Starting payment verification...');

    // Validate environment variables
    const razorpaySecret = process.env.RAZORPAY_SECRET;
    const supabaseUrl = process.env.VITE_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

    if (!razorpaySecret) {
      console.error('❌ [Verify Payment] Missing Razorpay secret');
      return res.status(500).json({
        success: false,
        error: 'Payment verification configuration error'
      });
    }

    // Extract payment details
    const {
      razorpay_order_id,
      razorpay_payment_id,
      razorpay_signature,
      order_id
    } = req.body;

    console.log('📝 [Verify Payment] Verification details:', {
      hasOrderId: !!razorpay_order_id,
      hasPaymentId: !!razorpay_payment_id,
      hasSignature: !!razorpay_signature,
      orderIdPrefix: razorpay_order_id ? razorpay_order_id.substring(0, 10) + '...' : null
    });

    // Validate required fields
    if (!razorpay_order_id || !razorpay_payment_id || !razorpay_signature) {
      console.error('❌ [Verify Payment] Missing required parameters');
      return res.status(400).json({
        success: false,
        error: 'Missing payment verification parameters'
      });
    }

    // Verify payment signature
    const body = razorpay_order_id + "|" + razorpay_payment_id;
    const expectedSignature = crypto
      .createHmac('sha256', razorpaySecret)
      .update(body.toString())
      .digest('hex');

    const isAuthentic = expectedSignature === razorpay_signature;

    console.log('🔍 [Verify Payment] Signature verification:', {
      isAuthentic,
      expectedPrefix: expectedSignature.substring(0, 10) + '...',
      receivedPrefix: razorpay_signature.substring(0, 10) + '...'
    });

    if (!isAuthentic) {
      console.error('❌ [Verify Payment] Invalid signature');
      return res.status(400).json({
        success: false,
        error: 'Payment verification failed: Invalid signature'
      });
    }

    // Update database if Supabase is available
    if (supabaseUrl && supabaseServiceKey) {
      try {
        const supabase = createClient(supabaseUrl, supabaseServiceKey);

        // Update payment record
        const { error: paymentError } = await supabase
          .from('payments')
          .update({
            razorpay_payment_id,
            razorpay_signature,
            status: 'captured',
            webhook_verified: true,
            updated_at: new Date().toISOString()
          })
          .eq('razorpay_order_id', razorpay_order_id);

        if (paymentError) {
          console.error('❌ [Verify Payment] Database update error:', paymentError);
        } else {
          console.log('✅ [Verify Payment] Payment record updated');
        }

        // Update order status if order_id is provided
        if (order_id) {
          const { error: orderError } = await supabase
            .from('orders')
            .update({
              payment_status: 'completed',
              razorpay_payment_id,
              updated_at: new Date().toISOString()
            })
            .eq('id', order_id);

          if (orderError) {
            console.error('❌ [Verify Payment] Order update error:', orderError);
          } else {
            console.log('✅ [Verify Payment] Order status updated');
          }
        }

      } catch (dbError) {
        console.error('❌ [Verify Payment] Database operation failed:', dbError);
        // Don't fail the verification if DB update fails
      }
    }

    console.log('✅ [Verify Payment] Payment verification successful');

    return res.status(200).json({
      success: true,
      message: 'Payment verified successfully',
      data: {
        razorpay_order_id,
        razorpay_payment_id,
        order_id
      }
    });

  } catch (error) {
    console.error('❌ [Verify Payment] Error:', error);
    
    return res.status(500).json({
      success: false,
      error: 'Payment verification failed',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}
