import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

import { Loader2, ShoppingCart, MapPin, CreditCard, Check, Smartphone, CreditCard as CreditCardIcon, RefreshCw } from 'lucide-react';
import { useCart } from '@/context/SupabaseCartContext';
import { useAuth } from '@/context/SupabaseAuthContext';
import { createOrder, Order, updateOrderWithPayment } from '@/services/orderService';
import { usePayment } from '@/hooks/usePayment';
import { toast } from '@/hooks/use-toast';
import CheckoutAddressStep from '@/components/checkout/CheckoutAddressStep';

import { useCODEnabled } from '@/hooks/useSettings';
import CODDisabledModal from '@/components/payment/CODDisabledModal';
import { formatShippingFee, ShippingCalculation } from '@/services/shippingService';

// Define checkout steps (simplified)
enum CheckoutStep {
  PAYMENT_AND_ADDRESS = 0,
  CONFIRMATION = 1,
}

const Checkout = () => {
  const navigate = useNavigate();
  const { cartItems, subtotal, clearCart } = useCart();
  const { isAuthenticated, user } = useAuth();
  const [currentStep, setCurrentStep] = useState<CheckoutStep>(CheckoutStep.PAYMENT_AND_ADDRESS);
  const [isLoading, setIsLoading] = useState(false);
  const [shippingAddress, setShippingAddress] = useState<any>(null);
  const [billingAddress, setBillingAddress] = useState<any>(null);
  const [paymentMethod, setPaymentMethod] = useState<string>('Cash on Delivery');
  const [orderId, setOrderId] = useState<string | null>(null);
  const [createdOrder, setCreatedOrder] = useState<Order | null>(null);
  const [showCODDisabledModal, setShowCODDisabledModal] = useState(false);
  const [showRefreshButton, setShowRefreshButton] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Shipping will be calculated in cart page
  const [shippingCalculation, setShippingCalculation] = useState<ShippingCalculation | null>(null);

  // Get COD availability from settings
  const { data: isCODEnabled, isLoading: isLoadingCOD } = useCODEnabled();

  // Update default payment method when COD availability changes
  useEffect(() => {
    if (!isLoadingCOD && isCODEnabled === false && paymentMethod === 'Cash on Delivery') {
      setPaymentMethod('Online Payment');
    }
  }, [isCODEnabled, isLoadingCOD, paymentMethod]);

  // Initialize payment hook
  const { processPayment, isLoading: isPaymentLoading } = usePayment({
    onSuccess: async (paymentId, orderId) => {
      console.log('🎉 Payment successful:', { paymentId, orderId });

      try {
        // For temporary orders, create the actual order
        if (orderId.startsWith('temp_')) {
          console.log('📦 Creating order for successful payment...');

          const shippingFee = shippingCalculation?.shippingFee || 0;
          const totalAmount = subtotal + shippingFee;

          // Get saved shipping info from cart
          const savedShipping = sessionStorage.getItem('checkoutShipping');
          const isBangaloreDelivery = savedShipping ? JSON.parse(savedShipping).isBangaloreDelivery : null;

          // Create the order in the database after successful payment
          const newOrder = await createOrder(
            user.id,
            cartItems,
            totalAmount,
            paymentMethod,
            shippingAddress,
            billingAddress,
            {
              shippingFee,
              isBangaloreDelivery,
              shippingNotes: shippingCalculation?.notes.join('; ') || '',
            }
          );

          if (newOrder) {
            console.log('✅ Order created successfully:', newOrder.id);

            // Update order with payment details
            await updateOrderWithPayment(newOrder.id, paymentId);

            // Store order ID for confirmation
            setOrderId(newOrder.id);
          } else {
            throw new Error('Failed to create order after payment');
          }
        } else {
          // For existing orders, just update with payment details
          await updateOrderWithPayment(orderId, paymentId);
          setOrderId(orderId);
        }

        // Move to confirmation step
        setCurrentStep(CheckoutStep.CONFIRMATION);

        // Clear cart
        clearCart(false);

        toast({
          title: 'Payment Successful',
          description: 'Your payment was successful and your order has been placed!',
        });
      } catch (error) {
        console.error('❌ Error handling successful payment:', error);
        toast({
          title: 'Order Creation Failed',
          description: `Payment was successful (ID: ${paymentId}) but order creation failed. Please contact support.`,
          variant: 'destructive'
        });
      }
    },
    onFailure: (error) => {
      console.error('❌ Payment failed:', error);
      toast({
        title: 'Payment Failed',
        description: 'Payment could not be processed. Please try again.',
        variant: 'destructive'
      });
    }
  });

  // Redirect to cart if cart is empty (but not during payment processing or confirmation)
  useEffect(() => {
    if (cartItems.length === 0 &&
        currentStep !== CheckoutStep.CONFIRMATION &&
        !isPaymentLoading &&
        !orderId) {
      navigate('/cart');
    }
  }, [cartItems, navigate, currentStep, isPaymentLoading, orderId]);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login?redirect=/checkout');
    }
  }, [isAuthenticated, navigate]);

  // Show refresh button after 10 seconds if payment is still loading
  useEffect(() => {
    if (isPaymentLoading) {
      const timer = setTimeout(() => {
        setShowRefreshButton(true);
        console.log('⏰ Showing refresh button after 10 seconds of payment processing');
      }, 10000); // 10 seconds

      return () => clearTimeout(timer);
    } else {
      setShowRefreshButton(false);
    }
  }, [isPaymentLoading]);

  // Load shipping calculation from cart page
  useEffect(() => {
    const savedShipping = sessionStorage.getItem('checkoutShipping');
    if (savedShipping) {
      try {
        const { calculation } = JSON.parse(savedShipping);
        setShippingCalculation(calculation);
      } catch (error) {
        console.error('Error parsing saved shipping data:', error);
        // Fallback calculation
        setShippingCalculation({
          shippingFee: 50,
          isFreeShipping: false,
          isManualCalculation: false,
          notes: ['Default shipping applied'],
          eligibleForFreeShipping: false,
        });
      }
    } else if (cartItems.length > 0) {
      // Fallback if no shipping data from cart
      setShippingCalculation({
        shippingFee: 50,
        isFreeShipping: false,
        isManualCalculation: false,
        notes: ['Please calculate shipping in cart first'],
        eligibleForFreeShipping: false,
      });
    }
  }, [cartItems]);

  // Handle address selection
  const handleAddressSelect = (shipping: any, billing: any) => {
    setShippingAddress(shipping);
    setBillingAddress(billing);
  };

  // Handle payment method selection
  const handlePaymentMethodSelect = (method: string) => {
    // If COD is selected but disabled, show modal
    if (method === 'Cash on Delivery' && !isCODEnabled) {
      setShowCODDisabledModal(true);
      return;
    }
    setPaymentMethod(method);
  };

  // Handle COD disabled modal actions
  const handleCODModalClose = () => {
    setShowCODDisabledModal(false);
  };

  const handleSelectOnlinePayment = () => {
    setPaymentMethod('Online Payment');
    setShowCODDisabledModal(false);
  };

  // Handle refresh during payment processing
  const handleRefresh = () => {
    console.log('🔄 Manual refresh triggered - reloading page');

    // Show message before refresh
    toast({
      title: 'Refreshing page...',
      description: 'Please wait while we refresh the page.',
    });

    // Trigger full page refresh
    window.location.reload();
  };

  // Handle place order
  const handlePlaceOrder = async () => {
    if (!user || !shippingAddress || !billingAddress) return;

    setIsLoading(true);
    try {
      // If payment method is Online Payment, process payment
      if (paymentMethod === 'Online Payment') {
        const shippingFee = shippingCalculation?.shippingFee || 0;
        const totalAmount = subtotal + shippingFee;

        // Prepare payment data for the new payment system
        const paymentData = {
          amount: totalAmount,
          cartItems,
          shippingAddress,
          billingAddress: billingAddress || shippingAddress,
          shippingFee,
          notes: shippingCalculation?.notes.join('; ') || ''
        };

        // Process payment using new payment system
        await processPayment(paymentData);

        setIsLoading(false);
        return;
      }

      // For COD and other payment methods, create order immediately
      const shippingFee = shippingCalculation?.shippingFee || 0;
      const totalAmount = subtotal + shippingFee;

      // Get saved shipping info from cart
      const savedShipping = sessionStorage.getItem('checkoutShipping');
      const isBangaloreDelivery = savedShipping ? JSON.parse(savedShipping).isBangaloreDelivery : null;

      // Create the order in the database with shipping information
      const newOrder = await createOrder(
        user.id,
        cartItems,
        totalAmount,
        paymentMethod,
        shippingAddress,
        billingAddress,
        {
          shippingFee,
          isBangaloreDelivery,
          shippingNotes: shippingCalculation?.notes.join('; ') || '',
        }
      );

      if (newOrder) {
        // Store order ID for confirmation
        setOrderId(newOrder.id);
        setCreatedOrder(newOrder);

        // Clear the cart (without showing toast since we show order success)
        clearCart(false);

        // Move to confirmation step
        setCurrentStep(CheckoutStep.CONFIRMATION);

        toast({
          title: 'Order Placed',
          description: 'Your order has been placed successfully!',
        });
      }
    } catch (error) {
      console.error('Error placing order:', error);
      toast({
        title: 'Error',
        description: 'Failed to place your order. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Render step indicator (simplified)
  const renderStepIndicator = () => {
    return (
      <div className="flex items-center justify-center mb-8">
        <div className="flex items-center">
          <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
            currentStep >= CheckoutStep.PAYMENT_AND_ADDRESS ? 'bg-badhees-600 text-white' : 'bg-gray-200'
          }`}>
            <CreditCard className="h-4 w-4" />
          </div>
          <div className={`w-12 h-1 ${
            currentStep > CheckoutStep.PAYMENT_AND_ADDRESS ? 'bg-badhees-600' : 'bg-gray-200'
          }`} />
        </div>

        <div className="flex items-center justify-center w-8 h-8 rounded-full ${
          currentStep >= CheckoutStep.CONFIRMATION ? 'bg-badhees-600 text-white' : 'bg-gray-200'
        }">
          <Check className="h-4 w-4" />
        </div>
      </div>
    );
  };



  // Render confirmation step
  const renderConfirmation = () => {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <Check className="h-8 w-8 text-green-600" />
        </div>
        <h2 className="text-2xl font-bold text-badhees-800 mb-4">Order Placed Successfully!</h2>
        <p className="text-badhees-600 mb-6">
          Thank you for your order. Your order number is <span className="font-bold">#{orderId?.substring(0, 8)}</span>.
        </p>
        <p className="text-badhees-600 mb-8">
          We'll send you an email confirmation shortly.
        </p>
        <div className="flex flex-col sm:flex-row justify-center gap-4 mobile-stack-buttons">
          <Button
            variant="outline"
            onClick={() => navigate('/orders')}
            className="py-6 text-base"
            size="lg"
          >
            View Order History
          </Button>
          <Button
            onClick={() => navigate('/products')}
            className="py-6 text-base"
            size="lg"
          >
            Continue Shopping
          </Button>
        </div>
      </div>
    );
  };

  // Render combined payment and address step
  const renderPaymentAndAddress = () => {
    return (
      <div className="space-y-4 md:space-y-6">
        {/* Address Section */}
        <div className="space-y-3 md:space-y-4">
          <h2 className="text-lg md:text-xl font-bold">Shipping Address</h2>
          <CheckoutAddressStep
            onAddressSelect={handleAddressSelect}
            onContinue={() => {}} // No-op since we're handling the continue action in this component
            onBack={() => navigate('/cart')}
          />
        </div>

        <div className="border-t border-badhees-100 pt-4 md:pt-6 mt-2 md:mt-4"></div>

        {/* Payment Section */}
        <div className="space-y-3 md:space-y-4">
          <h2 className="text-lg md:text-xl font-bold">Payment Method</h2>

          <div className="space-y-3">
            <div
              className={`border rounded-lg overflow-hidden ${
                paymentMethod === 'Cash on Delivery' ? 'border-gray-300' : 'border-gray-200'
              }`}
            >
              <div
                className="flex items-center justify-between w-full py-4 px-5 cursor-pointer bg-white hover:bg-gray-50 transition-colors"
                onClick={() => handlePaymentMethodSelect('Cash on Delivery')}
              >
                <div className="flex items-center text-base font-medium">
                  <ShoppingCart className="h-5 w-5 mr-3 text-gray-700" />
                  <span>Cash on Delivery</span>
                </div>
                {paymentMethod === 'Cash on Delivery' && isCODEnabled && (
                  <div className="text-badhees-600">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M20 6L9 17l-5-5" />
                    </svg>
                  </div>
                )}
              </div>
            </div>

            <div
              className={`border rounded-lg overflow-hidden ${paymentMethod === 'Online Payment' ? 'border-gray-300' : 'border-gray-200'}`}
            >
              <div
                className={`flex items-center justify-between w-full py-4 px-5 cursor-pointer ${paymentMethod === 'Online Payment' ? 'bg-white' : 'bg-white'}`}
                onClick={() => handlePaymentMethodSelect('Online Payment')}
              >
                <div className="flex items-center text-base font-medium">
                  <CreditCardIcon className="h-5 w-5 mr-3 text-gray-700" />
                  <span>Online Payment</span>
                  <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">
                    UPI, Cards
                  </span>
                </div>
                {paymentMethod === 'Online Payment' && (
                  <div className="text-badhees-600">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M20 6L9 17l-5-5" />
                    </svg>
                  </div>
                )}
              </div>
            </div>

          </div>
        </div>

        {/* Order Summary */}
        <div className="bg-badhees-50 p-3 rounded-lg shadow-sm">
          <h3 className="font-medium text-sm md:text-base mb-2">Order Summary</h3>
          <div className="flex justify-between mb-1.5 text-xs md:text-sm">
            <span>Subtotal:</span>
            <span>₹{subtotal.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</span>
          </div>
          <div className="flex justify-between mb-1.5 text-xs md:text-sm">
            <span>Shipping:</span>
            <span>
              {shippingCalculation ? formatShippingFee(shippingCalculation) : 'Calculating...'}
            </span>
          </div>
          {shippingCalculation?.isManualCalculation && (
            <div className="text-xs text-amber-600 mb-1.5">
              * Shipping fee will be calculated separately
            </div>
          )}
          <Separator className="my-2" />
          <div className="flex justify-between font-bold text-sm md:text-base">
            <span>Total:</span>
            <span>
              ₹{(subtotal + (shippingCalculation?.shippingFee || 0)).toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}
              {shippingCalculation?.isManualCalculation && (
                <span className="text-xs font-normal text-amber-600 ml-1">+ shipping</span>
              )}
            </span>
          </div>
        </div>

        {/* Place Order Button */}
        <div className="pt-3 md:pt-4 sticky bottom-0 bg-white pb-3 md:pb-0 md:static space-y-3">
          <Button
            onClick={handlePlaceOrder}
            disabled={isLoading || isPaymentLoading || !shippingAddress}
            className="w-full py-4 md:py-6 text-sm md:text-base shadow-md md:shadow-none"
            size="lg"
          >
            {isLoading || isPaymentLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 md:h-5 md:w-5 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                {paymentMethod === 'Online Payment' ? (
                  <>
                    <CreditCardIcon className="mr-2 h-4 w-4 md:h-5 md:w-5" />
                    Pay Now
                  </>
                ) : (
                  <>
                    <ShoppingCart className="mr-2 h-4 w-4 md:h-5 md:w-5" />
                    Place Order
                  </>
                )}
              </>
            )}
          </Button>

          {/* Refresh Button - Show after 10 seconds of payment processing */}
          {showRefreshButton && (isLoading || isPaymentLoading) && (
            <Button
              onClick={handleRefresh}
              disabled={isRefreshing}
              variant="outline"
              className="w-full py-3 text-sm"
            >
              {isRefreshing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Refreshing...
                </>
              ) : (
                <>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Refresh Status
                </>
              )}
            </Button>
          )}
        </div>
      </div>
    );
  };

  // Render current step
  const renderCurrentStep = () => {
    switch (currentStep) {
      case CheckoutStep.PAYMENT_AND_ADDRESS:
        return renderPaymentAndAddress();
      case CheckoutStep.CONFIRMATION:
        return renderConfirmation();
      default:
        return null;
    }
  };

  if (!isAuthenticated || (cartItems.length === 0 && currentStep !== CheckoutStep.CONFIRMATION)) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="flex-grow flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-badhees-600" />
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <div className="flex-grow pt-20 md:pt-28 pb-12 md:pb-16">
        <div className="max-w-3xl mx-auto px-3 sm:px-6">
          {currentStep !== CheckoutStep.CONFIRMATION && renderStepIndicator()}
          {renderCurrentStep()}
        </div>
      </div>
      <Footer />

      {/* COD Disabled Modal */}
      <CODDisabledModal
        isOpen={showCODDisabledModal}
        onClose={handleCODModalClose}
        onSelectOnlinePayment={handleSelectOnlinePayment}
      />
    </div>
  );
};

export default Checkout;
