/**
 * Payment Service
 * Handles all payment-related operations with Razorpay
 */

// Types
export interface RazorpayOrder {
  id: string;
  amount: number;
  currency: string;
  receipt: string;
  status: string;
  created_at: number;
}

export interface PaymentResponse {
  razorpay_payment_id: string;
  razorpay_order_id: string;
  razorpay_signature: string;
}

export interface CreateOrderRequest {
  amount: number;
  currency?: string;
  receipt: string;
  notes?: Record<string, any>;
}

export interface VerifyPaymentRequest {
  razorpay_payment_id: string;
  razorpay_order_id: string;
  razorpay_signature: string;
  order_id?: string;
}

// API Base URL helper
const getApiBaseUrl = (): string => {
  if (typeof window === 'undefined') return '';

  // In development, use the Vite proxy (same origin)
  // In production, use the same origin
  return window.location.origin;
};

/**
 * Load Razorpay script
 */
export const loadRazorpayScript = (): Promise<boolean> => {
  return new Promise((resolve) => {
    // Check if already loaded
    if (window.Razorpay) {
      resolve(true);
      return;
    }

    const script = document.createElement('script');
    script.src = 'https://checkout.razorpay.com/v1/checkout.js';
    script.onload = () => {
      console.log('✅ Razorpay script loaded successfully');
      resolve(true);
    };
    script.onerror = () => {
      console.error('❌ Failed to load Razorpay script');
      resolve(false);
    };
    document.body.appendChild(script);
  });
};

/**
 * Create a Razorpay order
 */
export const createRazorpayOrder = async (
  orderData: CreateOrderRequest
): Promise<RazorpayOrder> => {
  try {
    console.log('🚀 Creating Razorpay order:', orderData);

    const apiBaseUrl = getApiBaseUrl();
    const response = await fetch(`${apiBaseUrl}/api/create-order`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify(orderData),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Failed to create Razorpay order');
    }

    console.log('✅ Razorpay order created:', result.data);
    return result.data;

  } catch (error) {
    console.error('❌ Error creating Razorpay order:', error);
    throw new Error(`Failed to create payment order: ${error.message}`);
  }
};

/**
 * Verify Razorpay payment
 */
export const verifyRazorpayPayment = async (
  paymentData: VerifyPaymentRequest
): Promise<boolean> => {
  try {
    console.log('🔐 Verifying Razorpay payment...');

    const apiBaseUrl = getApiBaseUrl();
    const response = await fetch(`${apiBaseUrl}/api/verify-payment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify(paymentData),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Payment verification failed');
    }

    console.log('✅ Payment verification successful');
    return true;

  } catch (error) {
    console.error('❌ Error verifying payment:', error);
    throw new Error(`Payment verification failed: ${error.message}`);
  }
};

/**
 * Open Razorpay checkout
 */
export const openRazorpayCheckout = async (
  order: RazorpayOrder,
  options: {
    key: string;
    name: string;
    description: string;
    prefill: {
      name: string;
      email: string;
      contact: string;
    };
    notes?: Record<string, any>;
    theme?: {
      color: string;
    };
    onSuccess: (response: PaymentResponse) => void;
    onFailure: (error: any) => void;
  }
): Promise<void> => {
  try {
    console.log('💳 Opening Razorpay checkout...');

    // Ensure Razorpay script is loaded
    const scriptLoaded = await loadRazorpayScript();
    if (!scriptLoaded) {
      throw new Error('Failed to load payment gateway');
    }

    // Configure Razorpay options
    const razorpayOptions = {
      key: options.key,
      amount: order.amount,
      currency: order.currency,
      name: options.name,
      description: options.description,
      order_id: order.id,
      prefill: options.prefill,
      notes: options.notes || {},
      theme: options.theme || { color: '#3B82F6' },
      handler: function (response: PaymentResponse) {
        console.log('💳 Payment successful:', response);
        options.onSuccess(response);
      },
      modal: {
        ondismiss: function () {
          console.log('💳 Payment modal dismissed');
          options.onFailure(new Error('Payment cancelled by user'));
        }
      }
    };

    // Create and open Razorpay instance
    const razorpay = new window.Razorpay(razorpayOptions);
    razorpay.open();

  } catch (error) {
    console.error('❌ Error opening Razorpay checkout:', error);
    options.onFailure(error);
  }
};

/**
 * Check if device is mobile
 */
export const isMobileDevice = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  );
};

/**
 * Get payment method preferences for mobile
 */
export const getMobilePaymentConfig = () => {
  return {
    config: {
      display: {
        preferences: {
          show_default_blocks: true
        }
      }
    },
    retry: {
      enabled: true,
      max_count: 3
    }
  };
};

// Extend window interface for Razorpay
declare global {
  interface Window {
    Razorpay: any;
  }
}
