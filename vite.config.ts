import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
    // Add history API fallback for SPA routing
    historyApiFallback: true,
    proxy: {
      '/api': {
        // Use our payment API server
        target: `http://localhost:3002`,
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('Proxy error:', err);
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Sending Request to the Target:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
          });
        },
      },
    },
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // Vendor chunks
          'react-vendor': ['react', 'react-dom', 'react-router-dom'],
          'ui-vendor': ['@radix-ui/react-alert-dialog', '@radix-ui/react-dropdown-menu', '@radix-ui/react-dialog', '@radix-ui/react-select'],
          'query-vendor': ['@tanstack/react-query', '@tanstack/query-core'],
          'form-vendor': ['react-hook-form', '@hookform/resolvers', 'zod'],
          'chart-vendor': ['recharts', 'd3-scale', 'd3-array'],
          'date-vendor': ['date-fns'],
          'supabase-vendor': ['@supabase/supabase-js', '@supabase/auth-js', '@supabase/postgrest-js'],

          // Feature chunks
          'admin-dashboard': [
            './src/pages/AdminDashboard.tsx',
            './src/pages/AdminProducts.tsx',
            './src/pages/AdminOrders.tsx',
            './src/pages/AdminCustomers.tsx',
            './src/pages/AdminSettings.tsx',
            './src/pages/AdminEmployees.tsx',
            './src/pages/AdminEmployeeForm.tsx',
            './src/pages/AdminEmployeeDashboard.tsx',
            './src/pages/AdminAttendance.tsx',
            './src/pages/AdminLeave.tsx',
            './src/pages/AdminOvertime.tsx',
            './src/pages/AdminPayroll.tsx',
            './src/pages/AdminCompletedProjects.tsx',
            './src/pages/AdminCompletedProjectNew.tsx',
            './src/pages/AdminCompletedProjectEdit.tsx',
            './src/pages/AdminContactSubmissions.tsx',
            './src/pages/AdminConsultationRequests.tsx',
            './src/pages/AdminCustomizationRequests.tsx',
            './src/pages/AdminOrderDetails.tsx',
            './src/pages/AdminProductAdd.tsx',
            './src/pages/AdminProductEdit.tsx',
            './src/pages/AdminUserManagement.tsx'
          ],
          'editor-forms': [
            './src/components/editor/EditorProductForm.tsx'
          ],
          'auth-pages': [
            './src/pages/Login.tsx',
            './src/pages/Register.tsx',
            './src/pages/ForgotPassword.tsx',
            './src/pages/ResetPassword.tsx',
            './src/pages/EmailConfirmation.tsx'
          ],
          'shop-pages': [
            './src/pages/Shop.tsx',
            './src/pages/Products.tsx',
            './src/pages/ProductDetail.tsx',
            './src/pages/Cart.tsx',
            './src/pages/Checkout.tsx',
            './src/pages/Categories.tsx'
          ],
          'profile-orders': [
            './src/pages/Profile.tsx',
            './src/pages/Orders.tsx',
            './src/pages/OrderDetails.tsx'
          ],
          'info-pages': [
            './src/pages/About.tsx',
            './src/pages/Contact.tsx',
            './src/pages/FAQ.tsx',
            './src/pages/PrivacyPolicy.tsx',
            './src/pages/TermsOfService.tsx',
            './src/pages/ShippingReturns.tsx',
            './src/pages/Warranty.tsx',
            './src/pages/CareInstructions.tsx'
          ],
          'projects': [
            './src/pages/CustomInteriorProjects.tsx',
            './src/pages/CompletedProjects.tsx',
            './src/pages/CompletedProjectDetail.tsx'
          ]
        }
      }
    },
    chunkSizeWarningLimit: 1000, // Increase warning limit to 1MB - optimized for production
    target: 'esnext',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // Remove console.logs in production
        drop_debugger: true
      }
    }
  },

}));
