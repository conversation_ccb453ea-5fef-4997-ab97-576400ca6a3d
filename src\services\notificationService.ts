/**
 * Notification Service
 *
 * Handles admin notifications for new orders, messages, and requests.
 * Provides real-time updates and notification counts.
 */
import { supabase } from '@/lib/supabase';

export interface NotificationCounts {
  orders: number;
  customizationRequests: number;
  contactMessages: number;
  consultationRequests: number;
}

/**
 * Get count of new/unread orders (pending status)
 */
export const getNewOrdersCount = async (): Promise<number> => {
  try {
    const { count, error } = await supabase
      .from('orders')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'pending');

    if (error) {
      console.error('Error fetching new orders count:', error);
      return 0;
    }

    return count || 0;
  } catch (error) {
    console.error('Error in getNewOrdersCount:', error);
    return 0;
  }
};

/**
 * Get count of new/unread customization requests
 */
export const getNewCustomizationRequestsCount = async (): Promise<number> => {
  try {
    const { count, error } = await supabase
      .from('customization_requests')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'pending');

    if (error) {
      console.error('Error fetching customization requests count:', error);
      return 0;
    }

    return count || 0;
  } catch (error) {
    console.error('Error in getNewCustomizationRequestsCount:', error);
    return 0;
  }
};

/**
 * Get count of new/unread contact messages
 */
export const getNewContactMessagesCount = async (): Promise<number> => {
  try {
    const { count, error } = await supabase
      .from('contact_submissions')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'new');

    if (error) {
      console.error('Error fetching contact messages count:', error);
      return 0;
    }

    return count || 0;
  } catch (error) {
    console.error('Error in getNewContactMessagesCount:', error);
    return 0;
  }
};

/**
 * Get count of new/unread consultation requests
 */
export const getNewConsultationRequestsCount = async (): Promise<number> => {
  try {
    const { count, error } = await supabase
      .from('consultation_requests')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'pending');

    if (error) {
      console.error('Error fetching consultation requests count:', error);
      return 0;
    }

    return count || 0;
  } catch (error) {
    console.error('Error in getNewConsultationRequestsCount:', error);
    return 0;
  }
};

/**
 * Get all notification counts in a single call
 */
export const getAllNotificationCounts = async (): Promise<NotificationCounts> => {
  try {
    const [orders, customizationRequests, contactMessages, consultationRequests] = await Promise.all([
      getNewOrdersCount(),
      getNewCustomizationRequestsCount(),
      getNewContactMessagesCount(),
      getNewConsultationRequestsCount()
    ]);

    return {
      orders,
      customizationRequests,
      contactMessages,
      consultationRequests
    };
  } catch (error) {
    console.error('Error in getAllNotificationCounts:', error);
    return {
      orders: 0,
      customizationRequests: 0,
      contactMessages: 0,
      consultationRequests: 0
    };
  }
};

/**
 * Mark orders as viewed (when admin opens orders page)
 */
export const markOrdersAsViewed = async (): Promise<void> => {
  try {
    // Update pending orders to processing status when admin views them
    const { error } = await supabase
      .from('orders')
      .update({ status: 'processing' })
      .eq('status', 'pending');

    if (error) {
      console.error('Error marking orders as viewed:', error);
    }
  } catch (error) {
    console.error('Error in markOrdersAsViewed:', error);
  }
};

/**
 * Mark customization requests as viewed
 */
export const markCustomizationRequestsAsViewed = async (): Promise<void> => {
  try {
    const { error } = await supabase
      .from('customization_requests')
      .update({ status: 'in_progress' })
      .eq('status', 'pending');

    if (error) {
      console.error('Error marking customization requests as viewed:', error);
    }
  } catch (error) {
    console.error('Error in markCustomizationRequestsAsViewed:', error);
  }
};

/**
 * Mark contact messages as viewed
 */
export const markContactMessagesAsViewed = async (): Promise<void> => {
  try {
    const { error } = await supabase
      .from('contact_submissions')
      .update({ status: 'in_progress' })
      .eq('status', 'new');

    if (error) {
      console.error('Error marking contact messages as viewed:', error);
    }
  } catch (error) {
    console.error('Error in markContactMessagesAsViewed:', error);
  }
};

/**
 * Mark consultation requests as viewed
 */
export const markConsultationRequestsAsViewed = async (): Promise<void> => {
  try {
    const { error } = await supabase
      .from('consultation_requests')
      .update({ status: 'contacted' })
      .eq('status', 'pending');

    if (error) {
      console.error('Error marking consultation requests as viewed:', error);
    }
  } catch (error) {
    console.error('Error in markConsultationRequestsAsViewed:', error);
  }
};

// Global subscription manager to prevent duplicate subscriptions
let globalSubscription: (() => void) | null = null;
let subscriptionCount = 0;

/**
 * Subscribe to real-time notification updates
 */
export const subscribeToNotifications = (
  callback: (counts: NotificationCounts) => void
): (() => void) => {
  // If there's already a global subscription, just increment count and return cleanup
  if (globalSubscription) {
    subscriptionCount++;
    console.log(`📊 Reusing existing subscription (count: ${subscriptionCount})`);

    // Get initial counts for this subscriber
    getAllNotificationCounts().then(callback).catch(console.error);

    return () => {
      subscriptionCount--;
      console.log(`📊 Subscription cleanup (remaining: ${subscriptionCount})`);

      // Only cleanup global subscription when no more subscribers
      if (subscriptionCount <= 0 && globalSubscription) {
        globalSubscription();
        globalSubscription = null;
        subscriptionCount = 0;
        console.log('🔄 Global notification subscriptions cleaned up');
      }
    };
  }

  // Create new global subscription
  subscriptionCount = 1;
  console.log('🔄 Creating new global notification subscription');

  const channels: any[] = [];

  try {
    // Create unique channel names with timestamp to avoid conflicts
    const timestamp = Date.now();

    // Subscribe to orders changes
    const ordersChannel = supabase
      .channel(`orders-notifications-${timestamp}`)
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'orders' },
        async () => {
          try {
            const counts = await getAllNotificationCounts();
            callback(counts);
          } catch (error) {
            console.error('Error updating orders notifications:', error);
          }
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log('✅ Orders notifications subscribed');
        } else if (status === 'CHANNEL_ERROR') {
          console.error('❌ Orders notifications subscription error');
        }
      });
    channels.push(ordersChannel);

    // Subscribe to customization requests changes
    const customizationChannel = supabase
      .channel(`customization-notifications-${timestamp}`)
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'customization_requests' },
        async () => {
          try {
            const counts = await getAllNotificationCounts();
            callback(counts);
          } catch (error) {
            console.error('Error updating customization notifications:', error);
          }
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log('✅ Customization notifications subscribed');
        } else if (status === 'CHANNEL_ERROR') {
          console.error('❌ Customization notifications subscription error');
        }
      });
    channels.push(customizationChannel);

    // Subscribe to contact messages changes
    const contactChannel = supabase
      .channel(`contact-notifications-${timestamp}`)
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'contact_submissions' },
        async () => {
          try {
            const counts = await getAllNotificationCounts();
            callback(counts);
          } catch (error) {
            console.error('Error updating contact notifications:', error);
          }
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log('✅ Contact notifications subscribed');
        } else if (status === 'CHANNEL_ERROR') {
          console.error('❌ Contact notifications subscription error');
        }
      });
    channels.push(contactChannel);

    // Subscribe to consultation requests changes
    const consultationChannel = supabase
      .channel(`consultation-notifications-${timestamp}`)
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'consultation_requests' },
        async () => {
          try {
            const counts = await getAllNotificationCounts();
            callback(counts);
          } catch (error) {
            console.error('Error updating consultation notifications:', error);
          }
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log('✅ Consultation notifications subscribed');
        } else if (status === 'CHANNEL_ERROR') {
          console.error('❌ Consultation notifications subscription error');
        }
      });
    channels.push(consultationChannel);

  } catch (error) {
    console.error('Error setting up notification subscriptions:', error);
  }

  // Set global cleanup function
  globalSubscription = () => {
    try {
      channels.forEach(channel => {
        if (channel) {
          supabase.removeChannel(channel);
        }
      });
    } catch (error) {
      console.error('Error cleaning up notification subscriptions:', error);
    }
  };

  // Get initial counts
  getAllNotificationCounts().then(callback).catch(console.error);

  // Return cleanup function for this subscriber
  return () => {
    subscriptionCount--;
    console.log(`📊 Subscription cleanup (remaining: ${subscriptionCount})`);

    // Only cleanup global subscription when no more subscribers
    if (subscriptionCount <= 0 && globalSubscription) {
      globalSubscription();
      globalSubscription = null;
      subscriptionCount = 0;
      console.log('🔄 Global notification subscriptions cleaned up');
    }
  };
};
