// =====================================================
// TEST ORDER DATA DISPLAY
// =====================================================
// Run this in browser console to check order data

async function testOrderDataDisplay() {
  console.log('📋 TESTING ORDER DATA DISPLAY');
  console.log('==============================');
  
  try {
    // Check if user is logged in
    const { data: { session }, error: sessionError } = await window.supabase.auth.getSession();
    
    if (sessionError) {
      console.error('❌ Session error:', sessionError);
      return;
    }
    
    if (!session) {
      console.error('❌ User not logged in');
      return;
    }
    
    console.log('✅ User logged in:', session.user.email);
    
    // Get the most recent order
    console.log('🔍 Fetching most recent order...');
    const { data: orders, error: orderError } = await window.supabase
      .from('orders')
      .select(`
        id,
        user_id,
        status,
        payment_method,
        payment_status,
        total_amount,
        shipping_address,
        billing_address,
        razorpay_payment_id,
        payment_id,
        shipping_fee,
        shipping_notes,
        created_at
      `)
      .eq('user_id', session.user.id)
      .order('created_at', { ascending: false })
      .limit(1);
    
    if (orderError) {
      console.error('❌ Error fetching orders:', orderError);
      return;
    }
    
    if (!orders || orders.length === 0) {
      console.log('📭 No orders found for this user');
      return;
    }
    
    const order = orders[0];
    console.log('📦 Most recent order:', order.id);
    console.log('📅 Created at:', order.created_at);
    console.log('💰 Total amount:', order.total_amount);
    console.log('💳 Payment method:', order.payment_method);
    console.log('📊 Payment status:', order.payment_status);
    console.log('🏷️ Order status:', order.status);
    
    // Check payment IDs
    console.log('\n💳 PAYMENT ID ANALYSIS:');
    console.log('Razorpay Payment ID:', order.razorpay_payment_id || 'NULL');
    console.log('Payment ID:', order.payment_id || 'NULL');
    
    // Check address data
    console.log('\n📍 ADDRESS DATA ANALYSIS:');
    console.log('Shipping Address Type:', typeof order.shipping_address);
    console.log('Shipping Address:', order.shipping_address);
    
    if (order.shipping_address) {
      try {
        const shippingAddr = typeof order.shipping_address === 'string' 
          ? JSON.parse(order.shipping_address) 
          : order.shipping_address;
        console.log('Parsed Shipping Address:', shippingAddr);
        console.log('  - Name:', shippingAddr.name || 'Missing');
        console.log('  - Street:', shippingAddr.street || 'Missing');
        console.log('  - City:', shippingAddr.city || 'Missing');
        console.log('  - State:', shippingAddr.state || 'Missing');
        console.log('  - Postal Code:', shippingAddr.postal_code || 'Missing');
        console.log('  - Country:', shippingAddr.country || 'Missing');
        console.log('  - Phone:', shippingAddr.phone || 'Missing');
      } catch (parseError) {
        console.error('❌ Error parsing shipping address:', parseError);
      }
    } else {
      console.log('❌ Shipping address is NULL or empty');
    }
    
    console.log('\nBilling Address Type:', typeof order.billing_address);
    console.log('Billing Address:', order.billing_address);
    
    if (order.billing_address) {
      try {
        const billingAddr = typeof order.billing_address === 'string' 
          ? JSON.parse(order.billing_address) 
          : order.billing_address;
        console.log('Parsed Billing Address:', billingAddr);
        console.log('  - Name:', billingAddr.name || 'Missing');
        console.log('  - Street:', billingAddr.street || 'Missing');
        console.log('  - City:', billingAddr.city || 'Missing');
        console.log('  - State:', billingAddr.state || 'Missing');
        console.log('  - Postal Code:', billingAddr.postal_code || 'Missing');
        console.log('  - Country:', billingAddr.country || 'Missing');
        console.log('  - Phone:', billingAddr.phone || 'Missing');
      } catch (parseError) {
        console.error('❌ Error parsing billing address:', parseError);
      }
    } else {
      console.log('❌ Billing address is NULL or empty');
    }
    
    // Check order items
    console.log('\n📦 ORDER ITEMS:');
    const { data: orderItems, error: itemsError } = await window.supabase
      .from('order_items')
      .select(`
        id,
        product_id,
        quantity,
        price,
        total,
        products (
          id,
          name,
          price
        )
      `)
      .eq('order_id', order.id);
    
    if (itemsError) {
      console.error('❌ Error fetching order items:', itemsError);
    } else {
      console.log('📦 Order items count:', orderItems?.length || 0);
      if (orderItems && orderItems.length > 0) {
        orderItems.forEach(item => {
          console.log(`  - ${item.products?.name || 'Unknown Product'} (Qty: ${item.quantity}, Price: ₹${item.price})`);
        });
      }
    }
    
    // Check payments table
    console.log('\n💳 PAYMENTS TABLE:');
    const { data: payments, error: paymentsError } = await window.supabase
      .from('payments')
      .select('*')
      .eq('razorpay_payment_id', order.razorpay_payment_id);
    
    if (paymentsError) {
      console.error('❌ Error fetching payments:', paymentsError);
    } else {
      console.log('💳 Payment records found:', payments?.length || 0);
      if (payments && payments.length > 0) {
        payments.forEach(payment => {
          console.log(`  - Payment ID: ${payment.razorpay_payment_id}`);
          console.log(`  - Order ID: ${payment.razorpay_order_id}`);
          console.log(`  - Amount: ₹${payment.amount}`);
          console.log(`  - Status: ${payment.status}`);
        });
      }
    }
    
    console.log('\n🎉 Order data analysis completed!');
    
  } catch (error) {
    console.error('❌ Order data test failed:', error);
  }
}

// Run the test
testOrderDataDisplay();
