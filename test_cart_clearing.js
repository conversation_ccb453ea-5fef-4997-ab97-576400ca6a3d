// =====================================================
// TEST CART CLEARING FUNCTIONALITY
// =====================================================
// Run this in browser console to test cart clearing

async function testCartClearing() {
  console.log('🛒 TESTING CART CLEARING FUNCTIONALITY');
  console.log('=====================================');
  
  try {
    // Check if user is logged in
    const { data: { session }, error: sessionError } = await window.supabase.auth.getSession();
    
    if (sessionError) {
      console.error('❌ Session error:', sessionError);
      return;
    }
    
    if (!session) {
      console.error('❌ User not logged in');
      return;
    }
    
    console.log('✅ User logged in:', session.user.email);
    
    // Check current cart items
    console.log('🔍 Checking current cart items...');
    const { data: cartItems, error: cartError } = await window.supabase
      .from('cart_items')
      .select(`
        id,
        product_id,
        quantity,
        products (
          id,
          name,
          price
        )
      `)
      .eq('user_id', session.user.id);
    
    if (cartError) {
      console.error('❌ Error fetching cart:', cartError);
      return;
    }
    
    console.log('📦 Current cart items:', cartItems?.length || 0);
    if (cartItems && cartItems.length > 0) {
      cartItems.forEach(item => {
        console.log(`  - ${item.products.name} (Qty: ${item.quantity})`);
      });
    }
    
    // Test cart clearing using RPC function
    console.log('🧹 Testing cart clearing with RPC...');
    const { error: rpcError } = await window.supabase.rpc('clear_cart', {
      p_user_id: session.user.id
    });
    
    if (rpcError) {
      console.warn('⚠️ RPC clear_cart failed:', rpcError);
      
      // Fallback to direct delete
      console.log('🔄 Trying direct delete fallback...');
      const { error: deleteError } = await window.supabase
        .from('cart_items')
        .delete()
        .eq('user_id', session.user.id);
      
      if (deleteError) {
        console.error('❌ Direct delete also failed:', deleteError);
        return;
      } else {
        console.log('✅ Cart cleared using direct delete');
      }
    } else {
      console.log('✅ Cart cleared using RPC');
    }
    
    // Verify cart is empty
    console.log('🔍 Verifying cart is empty...');
    const { data: verifyCart, error: verifyError } = await window.supabase
      .from('cart_items')
      .select('id')
      .eq('user_id', session.user.id);
    
    if (verifyError) {
      console.error('❌ Error verifying cart:', verifyError);
      return;
    }
    
    if (verifyCart && verifyCart.length === 0) {
      console.log('✅ Cart successfully cleared!');
    } else {
      console.error('❌ Cart still has items:', verifyCart?.length || 0);
    }
    
    // Check localStorage as well
    console.log('🔍 Checking localStorage...');
    const guestCart = localStorage.getItem('cart_guest');
    const legacyCart = localStorage.getItem('cart');
    
    if (guestCart) {
      console.log('📦 Guest cart in localStorage:', JSON.parse(guestCart).length, 'items');
      localStorage.removeItem('cart_guest');
      console.log('🧹 Cleared guest cart from localStorage');
    }
    
    if (legacyCart) {
      console.log('📦 Legacy cart in localStorage:', JSON.parse(legacyCart).length, 'items');
      localStorage.removeItem('cart');
      console.log('🧹 Cleared legacy cart from localStorage');
    }
    
    if (!guestCart && !legacyCart) {
      console.log('✅ No cart data in localStorage');
    }
    
    console.log('🎉 Cart clearing test completed!');
    
  } catch (error) {
    console.error('❌ Cart clearing test failed:', error);
  }
}

// Run the test
testCartClearing();
