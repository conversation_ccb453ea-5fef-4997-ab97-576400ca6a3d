import React, { useState, useEffect } from 'react';

interface ProjectImageCarouselProps {
  images: string[];
  projectName: string;
}

const ProjectImageCarousel = ({ images, projectName }: ProjectImageCarouselProps) => {
  // Ensure images is an array and filter out empty or invalid URLs
  const validImages = Array.isArray(images)
    ? images.filter(url => url && typeof url === 'string' && url.trim() !== '')
    : [];

  // If no valid images, use placeholder
  const finalImages = validImages.length > 0
    ? validImages
    : ['/placeholder.svg'];

  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [imageLoaded, setImageLoaded] = useState<boolean[]>(finalImages.map(() => false));

  // Auto-change image every 4 seconds
  useEffect(() => {
    if (finalImages.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentImageIndex((prevIndex) => (prevIndex + 1) % finalImages.length);
    }, 4000);

    return () => clearInterval(interval);
  }, [finalImages.length]);

  // Handle image load events
  const handleImageLoad = (index: number) => {
    const newLoadedState = [...imageLoaded];
    newLoadedState[index] = true;
    setImageLoaded(newLoadedState);
  };

  // Handle image error events
  const handleImageError = (index: number, e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    console.log(`Image load error for ${projectName} image ${index}`);
    e.currentTarget.src = '/placeholder.svg';
    handleImageLoad(index);
  };

  // If only one image, show it without carousel
  if (finalImages.length === 1) {
    return (
      <div className="relative w-full h-full bg-badhees-100">
        <img
          src={finalImages[0]}
          alt={projectName}
          className="absolute inset-0 w-full h-full object-cover group-hover:scale-105"
          onLoad={() => handleImageLoad(0)}
          onError={(e) => handleImageError(0, e)}
          loading="lazy"
        />
      </div>
    );
  }

  // Multiple images, show carousel
  return (
    <div className="relative w-full h-full bg-badhees-100">
      {finalImages.map((image, index) => (
        <img
          key={index}
          src={image}
          alt={`${projectName} - Image ${index + 1}`}
          className={`absolute inset-0 w-full h-full object-cover transition-opacity duration-700 ${
            index === currentImageIndex ? 'opacity-100' : 'opacity-0'
          } group-hover:scale-105`}
          onLoad={() => handleImageLoad(index)}
          onError={(e) => handleImageError(index, e)}
          loading="lazy"
        />
      ))}
    </div>
  );
};

export default ProjectImageCarousel;
