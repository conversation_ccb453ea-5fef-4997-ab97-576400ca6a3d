import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { useAuth } from '@/context/SupabaseAuthContext';
import { supabase } from '@/lib/supabase';
import {
  EMOJI_TYPES,
  EmojiType,
  ProductVibesSummary,
  getProductVibesSummary,
  getUserVibeForProduct,
  toggleUserVibe,
  subscribeToProductVibes
} from '@/services/productVibesService';

interface EmojiReactionsProps {
  productId: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showCounts?: boolean;
  interactive?: boolean; // Whether users can click to react (only on product detail page)
  layout?: 'horizontal' | 'compact'; // Layout style
}

const EmojiReactions: React.FC<EmojiReactionsProps> = ({
  productId,
  className,
  size = 'md',
  showCounts = true,
  interactive = false,
  layout = 'horizontal'
}) => {
  const { user, isAuthenticated } = useAuth();
  const [vibesSummary, setVibesSummary] = useState<ProductVibesSummary | null>(null);
  const [userVibe, setUserVibe] = useState<EmojiType | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  // Size configurations
  const sizeConfig = {
    sm: {
      emoji: 'text-sm',
      count: 'text-xs',
      button: 'p-1',
      gap: 'gap-1'
    },
    md: {
      emoji: 'text-base',
      count: 'text-xs',
      button: 'p-1.5',
      gap: 'gap-1.5'
    },
    lg: {
      emoji: 'text-lg',
      count: 'text-sm',
      button: 'p-2',
      gap: 'gap-2'
    }
  };

  const config = sizeConfig[size];

  // Load initial data
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsInitialLoading(true);
        // Load vibes summary
        const summary = await getProductVibesSummary(productId);
        setVibesSummary(summary);

        // Load user's vibe if authenticated
        if (user && isAuthenticated) {
          const userVibeType = await getUserVibeForProduct(productId, user.id);
          setUserVibe(userVibeType);
        }
      } catch (error) {
        console.error('Error loading vibe data:', error);
      } finally {
        setIsInitialLoading(false);
      }
    };

    loadData();
  }, [productId, user, isAuthenticated]);

  // Subscribe to real-time updates
  useEffect(() => {
    let subscription: any = null;

    // Only subscribe if interactive (to avoid multiple subscriptions on the same page)
    if (interactive) {
      // Create a unique channel name to avoid conflicts
      const channelName = `product-vibes-${productId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      subscription = supabase
        .channel(channelName)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'vibes',
            filter: `product_id=eq.${productId}`
          },
          async () => {
            // Fetch updated summary when vibes change
            const { getProductVibesSummary } = await import('@/services/productVibesService');
            const summary = await getProductVibesSummary(productId);
            setVibesSummary(summary);
          }
        )
        .subscribe();
    }

    return () => {
      if (subscription) {
        subscription.unsubscribe();
      }
    };
  }, [productId, interactive]);

  // Handle emoji click
  const handleEmojiClick = async (emojiType: EmojiType) => {
    if (!interactive) return;
    
    if (!isAuthenticated) {
      // Could show login modal here
      return;
    }

    setIsLoading(true);
    try {
      const success = await toggleUserVibe(productId, emojiType);
      if (success) {
        // Update user vibe optimistically
        setUserVibe(userVibe === emojiType ? null : emojiType);
      }
    } catch (error) {
      console.error('Error toggling vibe:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Get count for specific emoji type
  const getEmojiCount = (emojiType: EmojiType): number => {
    if (!vibesSummary) return 0;
    
    switch (emojiType) {
      case 'love': return vibesSummary.loveCount;
      case 'fire': return vibesSummary.fireCount;
      case 'star_eyes': return vibesSummary.starEyesCount;
      case 'neutral': return vibesSummary.neutralCount;
      case 'thumbs_down': return vibesSummary.thumbsDownCount;
      default: return 0;
    }
  };

  // Show loading state briefly
  if (isInitialLoading) {
    return (
      <div className={cn('flex items-center', config.gap, className)}>
        <div className="text-gray-400 text-sm">Loading...</div>
      </div>
    );
  }

  // Don't render if no vibes and not interactive
  if (!interactive && (!vibesSummary || vibesSummary.totalVibes === 0)) {
    return null;
  }

  const renderEmoji = (emojiType: EmojiType) => {
    const count = getEmojiCount(emojiType);
    const isSelected = userVibe === emojiType;
    const hasCount = count > 0;

    // Don't render if no count and not interactive
    if (!interactive && count === 0) {
      return null;
    }

    const emojiElement = (
      <div
        className={cn(
          'flex items-center',
          config.gap,
          interactive && 'cursor-pointer transition-all duration-200',
          interactive && 'hover:scale-110',
          isSelected && interactive && 'scale-110',
          !interactive && !hasCount && 'opacity-50'
        )}
        onClick={() => handleEmojiClick(emojiType)}
        {...(interactive && {
          role: 'button',
          tabIndex: 0,
          'aria-label': `React with ${EMOJI_TYPES[emojiType]} emoji`
        })}
        onKeyDown={(e) => {
          if (interactive && (e.key === 'Enter' || e.key === ' ')) {
            e.preventDefault();
            handleEmojiClick(emojiType);
          }
        }}
      >
        <span 
          className={cn(
            config.emoji,
            interactive && isSelected && 'drop-shadow-lg',
            isLoading && 'opacity-50'
          )}
        >
          {EMOJI_TYPES[emojiType]}
        </span>
        {showCounts && hasCount && (
          <span 
            className={cn(
              config.count,
              'text-badhees-600 font-medium',
              isSelected && 'text-badhees-800'
            )}
          >
            {count}
          </span>
        )}
      </div>
    );

    return emojiElement;
  };

  if (layout === 'compact') {
    // Compact layout - show only emojis with counts, no spacing
    const emojisWithCounts = Object.keys(EMOJI_TYPES).filter(emojiType => 
      interactive || getEmojiCount(emojiType as EmojiType) > 0
    ) as EmojiType[];

    if (!interactive && emojisWithCounts.length === 0) {
      return null;
    }

    return (
      <div className={cn('flex items-center', config.gap, className)}>
        {emojisWithCounts.map((emojiType) => (
          <div key={emojiType}>
            {renderEmoji(emojiType)}
          </div>
        ))}
      </div>
    );
  }

  // Horizontal layout - show all emojis
  return (
    <div className={cn('flex items-center', config.gap, className)}>
      {Object.keys(EMOJI_TYPES).map((emojiType) => (
        <div key={emojiType}>
          {renderEmoji(emojiType as EmojiType)}
        </div>
      ))}
    </div>
  );
};

export default EmojiReactions;
