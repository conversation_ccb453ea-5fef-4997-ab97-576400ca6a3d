/**
 * Order Tracker Component
 *
 * This component displays the current status of an order with a visual timeline.
 */
import React from 'react';
import { cn } from '@/lib/utils';
import { CheckCircle, Clock, Package, Truck, XCircle } from 'lucide-react';
import '@/styles/order-tracker.css';

interface OrderTrackerProps {
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  className?: string;
}

const OrderTracker: React.FC<OrderTrackerProps> = ({
  status,
  className
}) => {
  // Define the steps in the order process
  const steps = [
    {
      id: 'pending',
      label: 'Pending',
      description: 'Order placed',
      icon: Clock,
      complete: ['pending', 'processing', 'shipped', 'delivered', 'paid'].includes(status)
    },
    {
      id: 'processing',
      label: 'Processing',
      description: 'Order is being prepared',
      icon: Package,
      complete: ['processing', 'shipped', 'delivered', 'paid'].includes(status)
    },
    {
      id: 'shipped',
      label: 'Shipped',
      description: 'Order has been shipped',
      icon: Truck,
      complete: ['shipped', 'delivered', 'paid'].includes(status)
    },
    {
      id: 'delivered',
      label: 'Delivered',
      description: 'Order has been delivered',
      icon: CheckCircle,
      complete: ['delivered'].includes(status)
    }
  ];

  // If the order is cancelled, show a different view
  if (status === 'cancelled') {
    return (
      <div className={cn("order-tracker-canceled", className)}>
        <div className="flex items-center space-x-3">
          <XCircle className="h-8 w-8 text-red-500" />
          <div>
            <h3 className="font-medium text-red-700">Order Cancelled</h3>
            <p className="text-sm text-red-600">This order has been cancelled</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("w-full", className)}>
      <div className="relative">
        {/* Progress bar */}
        <div className="absolute left-0 top-5 w-full h-1 bg-gray-200">
          <div className={cn(
            "order-tracker-progress",
            status === 'pending' ? "order-tracker-progress-pending" :
            status === 'processing' ? "order-tracker-progress-processing" :
            status === 'shipped' ? "order-tracker-progress-shipped" :
            status === 'paid' ? "order-tracker-progress-delivered" :
            "order-tracker-progress-delivered"
          )} />
        </div>

        {/* Steps */}
        <div className="relative flex justify-between">
          {steps.map((step) => (
            <div key={step.id} className="order-tracker-step">
              <div className={cn(
                "order-tracker-step-icon",
                step.complete ? "order-tracker-step-active" : "order-tracker-step-inactive"
              )}>
                <step.icon className="h-5 w-5" />
              </div>
              <div className="order-tracker-step-label">
                <div className={cn(
                  "order-tracker-step-text",
                  step.complete ? "text-badhees-600" : "text-gray-500"
                )}>
                  {step.label}
                </div>
                <div className="order-tracker-step-description">
                  {step.description}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default OrderTracker;
