// =====================================================
// TEST MOBILE PAYMENT DATA STORAGE
// =====================================================
// Run this in browser console to check what's stored

async function testMobilePaymentData() {
  console.log('📱 TESTING MOBILE PAYMENT DATA STORAGE');
  console.log('======================================');
  
  try {
    // Check if there's stored mobile payment data
    const storedData = localStorage.getItem('mobile_payment_data');
    
    if (storedData) {
      console.log('📦 Found stored mobile payment data');
      try {
        const parsedData = JSON.parse(storedData);
        console.log('📋 Stored payment data:', parsedData);
        
        console.log('\n🔍 DETAILED ANALYSIS:');
        console.log('User ID:', parsedData.userId || 'Missing');
        console.log('User Email:', parsedData.userEmail || 'Missing');
        console.log('Amount:', parsedData.amount || 'Missing');
        console.log('Cart Items Count:', parsedData.cartItems?.length || 0);
        
        console.log('\n📍 ADDRESS DATA:');
        console.log('Shipping Address Type:', typeof parsedData.shippingAddress);
        console.log('Shipping Address:', parsedData.shippingAddress);
        
        if (parsedData.shippingAddress && typeof parsedData.shippingAddress === 'object') {
          console.log('  - Name:', parsedData.shippingAddress.name || 'Missing');
          console.log('  - Street:', parsedData.shippingAddress.street || 'Missing');
          console.log('  - City:', parsedData.shippingAddress.city || 'Missing');
          console.log('  - State:', parsedData.shippingAddress.state || 'Missing');
          console.log('  - Postal Code:', parsedData.shippingAddress.postal_code || 'Missing');
          console.log('  - Country:', parsedData.shippingAddress.country || 'Missing');
          console.log('  - Phone:', parsedData.shippingAddress.phone || 'Missing');
        }
        
        console.log('\nBilling Address Type:', typeof parsedData.billingAddress);
        console.log('Billing Address:', parsedData.billingAddress);
        
        if (parsedData.billingAddress && typeof parsedData.billingAddress === 'object') {
          console.log('  - Name:', parsedData.billingAddress.name || 'Missing');
          console.log('  - Street:', parsedData.billingAddress.street || 'Missing');
          console.log('  - City:', parsedData.billingAddress.city || 'Missing');
          console.log('  - State:', parsedData.billingAddress.state || 'Missing');
          console.log('  - Postal Code:', parsedData.billingAddress.postal_code || 'Missing');
          console.log('  - Country:', parsedData.billingAddress.country || 'Missing');
          console.log('  - Phone:', parsedData.billingAddress.phone || 'Missing');
        }
        
        console.log('\n💳 PAYMENT DATA:');
        console.log('Razorpay Order ID:', parsedData.razorpayOrderId || 'Missing');
        console.log('Shipping Fee:', parsedData.shippingFee || 0);
        console.log('Notes:', parsedData.notes || 'None');
        console.log('Timestamp:', new Date(parsedData.timestamp).toLocaleString());
        
        console.log('\n📦 CART ITEMS:');
        if (parsedData.cartItems && parsedData.cartItems.length > 0) {
          parsedData.cartItems.forEach((item, index) => {
            console.log(`  ${index + 1}. ${item.product?.name || 'Unknown Product'} (Qty: ${item.quantity})`);
            console.log(`     Price: ₹${item.product?.price || 'Unknown'}`);
            console.log(`     Product ID: ${item.product?.id || 'Unknown'}`);
          });
        } else {
          console.log('  No cart items found');
        }
        
        // Check data age
        const dataAge = Date.now() - parsedData.timestamp;
        const ageMinutes = Math.floor(dataAge / (1000 * 60));
        console.log('\n⏰ DATA AGE:');
        console.log(`Data is ${ageMinutes} minutes old`);
        console.log(`Max age allowed: 30 minutes`);
        console.log(`Is valid: ${dataAge < (30 * 60 * 1000) ? 'YES' : 'NO'}`);
        
      } catch (parseError) {
        console.error('❌ Error parsing stored data:', parseError);
        console.log('Raw stored data:', storedData);
      }
    } else {
      console.log('📭 No mobile payment data found in localStorage');
    }
    
    // Check for failed order data as well
    const failedOrderData = localStorage.getItem('failed_order_data');
    if (failedOrderData) {
      console.log('\n🚨 FAILED ORDER DATA FOUND:');
      try {
        const parsedFailedData = JSON.parse(failedOrderData);
        console.log('Failed order data:', parsedFailedData);
      } catch (e) {
        console.error('Error parsing failed order data:', e);
      }
    }
    
    // Check URL parameters for payment completion
    console.log('\n🔗 URL PARAMETERS:');
    const urlParams = new URLSearchParams(window.location.search);
    const paymentId = urlParams.get('razorpay_payment_id');
    const orderId = urlParams.get('razorpay_order_id');
    const signature = urlParams.get('razorpay_signature');
    
    console.log('Payment ID in URL:', paymentId || 'Not found');
    console.log('Order ID in URL:', orderId || 'Not found');
    console.log('Signature in URL:', signature || 'Not found');
    
    console.log('\n🎉 Mobile payment data analysis completed!');
    
  } catch (error) {
    console.error('❌ Mobile payment data test failed:', error);
  }
}

// Run the test
testMobilePaymentData();
