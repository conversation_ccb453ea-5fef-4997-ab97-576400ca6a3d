import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import AdminSidebar from "@/components/admin/AdminSidebar";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from '@/components/ui/button';
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Search, Filter, Eye, Loader2, Calendar, Mail, Phone } from 'lucide-react';
import { useAuth } from '@/context/SupabaseAuthContext';
import { toast } from '@/hooks/use-toast';
import { supabase } from '@/lib/supabase';
import {
  getConsultationRequests,
  updateConsultationRequestStatus,
  ConsultationRequest
} from '@/services/consultationRequestService';

const AdminConsultationRequests = () => {
  const [requests, setRequests] = useState<ConsultationRequest[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedRequest, setSelectedRequest] = useState<ConsultationRequest | null>(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const navigate = useNavigate();
  const { isAuthenticated, isAdmin } = useAuth();

  useEffect(() => {
    window.scrollTo(0, 0);

    // Check if user has access to this page
    if (!isAuthenticated) {
      navigate('/login?redirect=/admin/consultation-requests');
    } else if (!isAdmin()) {
      navigate('/');
    } else {
      fetchRequests();
    }
  }, [isAuthenticated, isAdmin, navigate, statusFilter]);

  const fetchRequests = async () => {
    try {
      setIsLoading(true);
      const status = statusFilter !== 'all' ? statusFilter : undefined;
      const data = await getConsultationRequests(status);
      setRequests(data);
    } catch (error) {
      console.error('Error fetching consultation requests:', error);
      toast({
        title: 'Error',
        description: 'Failed to load consultation requests. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Set up real-time subscription for consultation requests
  useEffect(() => {
    if (!isAuthenticated || !isAdmin()) return;

    const timestamp = Date.now();
    const channel = supabase
      .channel(`consultation_requests_admin_${timestamp}`)
      .on(
        'postgres_changes',
        {
          event: '*', // Listen to all events (INSERT, UPDATE, DELETE)
          schema: 'public',
          table: 'consultation_requests'
        },
        async (payload) => {
          console.log('Consultation request change:', payload);

          try {
            await fetchRequests();

            // Show notification for new requests
            if (payload.eventType === 'INSERT') {
              toast({
                title: 'New Consultation Request',
                description: `New request from ${payload.new.name}`,
                duration: 5000,
              });
            } else if (payload.eventType === 'UPDATE') {
              toast({
                title: 'Request Updated',
                description: `Request status updated`,
                duration: 3000,
              });
            }
          } catch (error) {
            console.error('Error handling consultation request change:', error);
          }
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log('✅ Consultation requests real-time subscription active');
        } else if (status === 'CHANNEL_ERROR') {
          console.error('❌ Consultation requests subscription error');
        }
      });

    return () => {
      try {
        supabase.removeChannel(channel);
        console.log('🔄 Consultation requests subscription cleaned up');
      } catch (error) {
        console.error('Error cleaning up consultation requests subscription:', error);
      }
    };
  }, [isAuthenticated, isAdmin]);

  const handleViewRequest = (request: ConsultationRequest) => {
    setSelectedRequest(request);
    setIsViewDialogOpen(true);
  };

  const handleUpdateStatus = async (status: ConsultationRequest['status']) => {
    if (!selectedRequest) return;

    setIsUpdatingStatus(true);
    try {
      const success = await updateConsultationRequestStatus(selectedRequest.id, status);
      if (success) {
        // Update the local state
        setRequests(prevRequests =>
          prevRequests.map(req =>
            req.id === selectedRequest.id ? { ...req, status } : req
          )
        );
        setSelectedRequest({ ...selectedRequest, status });
      }
    } catch (error) {
      console.error('Error updating status:', error);
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  // Filter requests based on search query
  const filteredRequests = requests.filter(request => {
    const searchLower = searchQuery.toLowerCase();
    return (
      request.name.toLowerCase().includes(searchLower) ||
      request.email.toLowerCase().includes(searchLower) ||
      (request.phone && request.phone.includes(searchQuery))
    );
  });

  // Helper function to get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Pending</Badge>;
      case 'contacted':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800 hover:bg-blue-100">Contacted</Badge>;
      case 'scheduled':
        return <Badge variant="outline" className="bg-purple-100 text-purple-800 hover:bg-purple-100">Scheduled</Badge>;
      case 'completed':
        return <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-100">Completed</Badge>;
      case 'cancelled':
        return <Badge variant="outline" className="bg-red-100 text-red-800 hover:bg-red-100">Cancelled</Badge>;
      case 'on_hold':
        return <Badge variant="outline" className="bg-orange-100 text-orange-800 hover:bg-orange-100">On Hold</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  if (!isAuthenticated || !isAdmin()) {
    return (
      <div className="min-h-screen">
        <Navbar />
        <div className="pt-28 pb-16 flex items-center justify-center">
          <div className="max-w-md w-full p-8 bg-white rounded-lg shadow-md">
            <h1 className="text-2xl font-bold text-center mb-6">Admin Access Required</h1>
            <p className="text-badhees-600 mb-6 text-center">
              You need to be logged in as an admin to access this page.
            </p>
            <div className="flex justify-center">
              <Button asChild className="w-full">
                <a href="/login?redirect=/admin/consultation-requests">Login</a>
              </Button>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex">
      <AdminSidebar />

      <div className="flex-1">
        <Navbar />

        <div className="pt-28 pb-16 px-4 sm:px-8 max-w-[1400px] mx-auto">
          <h1 className="text-2xl md:text-3xl font-bold text-badhees-800 mb-6">
            Consultation Requests
          </h1>

          <Card>
            <CardHeader>
              <CardTitle>Manage Consultation Requests</CardTitle>
              <CardDescription>
                View and manage customer consultation requests from the website.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col sm:flex-row gap-4 mb-6">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
                  <Input
                    placeholder="Search by name, email, or phone..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <div className="flex items-center gap-2">
                  <Filter size={18} className="text-gray-500" />
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="contacted">Contacted</SelectItem>
                      <SelectItem value="scheduled">Scheduled</SelectItem>
                      <SelectItem value="on_hold">On Hold</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="cancelled">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {isLoading ? (
                <div className="flex justify-center items-center py-12">
                  <Loader2 className="h-8 w-8 animate-spin text-badhees-600" />
                  <span className="ml-3 text-badhees-600">Loading requests...</span>
                </div>
              ) : filteredRequests.length > 0 ? (
                <div className="rounded-md border overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[180px]">Name</TableHead>
                        <TableHead>Email</TableHead>
                        <TableHead>Project Type</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredRequests.map((request) => (
                        <TableRow key={request.id}>
                          <TableCell className="font-medium">{request.name}</TableCell>
                          <TableCell>{request.email}</TableCell>
                          <TableCell>
                            {request.project_type ? (
                              request.project_type
                                .replace('_', ' ')
                                .split(' ')
                                .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                                .join(' ')
                            ) : (
                              'Not specified'
                            )}
                          </TableCell>
                          <TableCell>{formatDate(request.created_at)}</TableCell>
                          <TableCell>{getStatusBadge(request.status)}</TableCell>
                          <TableCell className="text-right">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleViewRequest(request)}
                            >
                              <Eye className="h-4 w-4 mr-1" />
                              View
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="text-center py-12 text-badhees-600">
                  {searchQuery ? (
                    <p>No consultation requests found matching your search.</p>
                  ) : statusFilter !== 'all' ? (
                    <p>No consultation requests with status "{statusFilter}".</p>
                  ) : (
                    <p>No consultation requests found.</p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* View Request Dialog */}
      {selectedRequest && (
        <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Consultation Request Details</DialogTitle>
              <DialogDescription>
                Request from {selectedRequest.name} on {formatDate(selectedRequest.created_at)}
              </DialogDescription>
            </DialogHeader>

            <div className="grid gap-6 py-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-1">Contact Information</h3>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <Mail className="h-4 w-4 mr-2 text-badhees-600" />
                      <a href={`mailto:${selectedRequest.email}`} className="text-badhees-600 hover:underline">
                        {selectedRequest.email}
                      </a>
                    </div>
                    {selectedRequest.phone && (
                      <div className="flex items-center">
                        <Phone className="h-4 w-4 mr-2 text-badhees-600" />
                        <a href={`tel:${selectedRequest.phone}`} className="text-badhees-600 hover:underline">
                          {selectedRequest.phone}
                        </a>
                      </div>
                    )}
                  </div>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-1">Project Details</h3>
                  <div className="space-y-2">
                    <div className="flex items-start">
                      <Calendar className="h-4 w-4 mr-2 text-badhees-600 mt-0.5" />
                      <div>
                        <span className="text-badhees-800 font-medium">Project Type:</span>{' '}
                        {selectedRequest.project_type ? (
                          selectedRequest.project_type
                            .replace('_', ' ')
                            .split(' ')
                            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                            .join(' ')
                        ) : (
                          'Not specified'
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {selectedRequest.message && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Message</h3>
                  <div className="bg-gray-50 p-3 rounded-md whitespace-pre-wrap">
                    {selectedRequest.message}
                  </div>
                </div>
              )}

              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">Status</h3>
                <div className="flex items-center justify-between">
                  <div>{getStatusBadge(selectedRequest.status)}</div>
                  <div className="flex gap-2">
                    <Select
                      value={selectedRequest.status}
                      onValueChange={(value) => handleUpdateStatus(value as ConsultationRequest['status'])}
                      disabled={isUpdatingStatus}
                    >
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Update status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="pending">Pending</SelectItem>
                        <SelectItem value="contacted">Contacted</SelectItem>
                        <SelectItem value="scheduled">Scheduled</SelectItem>
                        <SelectItem value="on_hold">On Hold</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                        <SelectItem value="cancelled">Cancelled</SelectItem>
                      </SelectContent>
                    </Select>
                    {isUpdatingStatus && <Loader2 className="h-4 w-4 animate-spin" />}
                  </div>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button onClick={() => setIsViewDialogOpen(false)}>Close</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default AdminConsultationRequests;
