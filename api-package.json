{"name": "thebadhees-payment-api", "version": "2.0.0", "description": "Payment API server for The Badhees ecommerce platform", "type": "module", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "test": "echo \"No tests specified\" && exit 0"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "razorpay": "^2.9.2", "@supabase/supabase-js": "^2.38.4"}, "engines": {"node": ">=18.0.0"}, "keywords": ["payment", "razorpay", "ecommerce", "api", "nodejs", "express"], "author": "The Badhees", "license": "MIT"}