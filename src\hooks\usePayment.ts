/**
 * Payment Hook
 * Handles Razorpay payment processing with proper error handling and order creation
 */

import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { useAuth } from '@/context/SupabaseAuthContext';
import { useCart } from '@/context/SupabaseCartContext';
import { 
  createRazorpayOrder, 
  verifyRazorpayPayment, 
  openRazorpayCheckout,
  isMobileDevice,
  getMobilePaymentConfig,
  PaymentResponse,
  CreateOrderRequest
} from '@/services/paymentService';
import { createOrder, createOrderAfterPayment, Order } from '@/services/orderService';

interface UsePaymentOptions {
  onSuccess?: (orderId: string, paymentId: string) => void;
  onFailure?: (error: any) => void;
}

interface PaymentData {
  amount: number;
  cartItems: any[];
  shippingAddress: any;
  billingAddress?: any;
  shippingFee?: number;
  notes?: string;
}

export function usePayment(options: UsePaymentOptions = {}) {
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuth();
  const { clearCart } = useCart();
  const navigate = useNavigate();

  /**
   * Process payment for the given order data
   */
  const processPayment = async (paymentData: PaymentData) => {
    if (!user) {
      toast.error('You need to be logged in to make a payment.');
      return;
    }

    setIsLoading(true);

    try {
      console.log('🚀 Starting payment process...');

      // Validate environment variables
      const razorpayKeyId = import.meta.env.VITE_RAZORPAY_KEY_ID;
      if (!razorpayKeyId) {
        throw new Error('Payment gateway not configured');
      }

      // Generate a temporary receipt ID for Razorpay order
      const tempReceiptId = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;

      // Create Razorpay order FIRST (without database order)
      console.log('💳 Creating Razorpay order...');
      const razorpayOrderData: CreateOrderRequest = {
        amount: paymentData.amount,
        currency: 'INR',
        receipt: tempReceiptId.substring(0, 39), // Keep under 40 chars
        notes: {
          user_id: user.id,
          user_email: user.email,
          item_count: paymentData.cartItems.length,
          cart_data: JSON.stringify(paymentData.cartItems.map(item => ({
            product_id: item.product.id,
            quantity: item.quantity,
            price: item.product.isSale && item.product.salePrice ? item.product.salePrice : item.product.price
          })))
        }
      };

      const razorpayOrder = await createRazorpayOrder(razorpayOrderData);

      // Store payment data temporarily for order creation after payment success
      const tempPaymentData = {
        ...paymentData,
        razorpayOrderId: razorpayOrder.id
      };

      // Configure payment options
      const isMobile = isMobileDevice();
      const paymentOptions = {
        key: razorpayKeyId,
        name: 'The Badhees',
        description: `Payment for ₹${paymentData.amount}`,
        prefill: {
          name: user.displayName || user.name || '',
          email: user.email || '',
          contact: paymentData.shippingAddress?.phone || ''
        },
        notes: {
          user_id: user.id
        },
        theme: {
          color: '#3B82F6'
        },
        onSuccess: (response: PaymentResponse) => {
          handlePaymentSuccess(response, tempPaymentData);
        },
        onFailure: (error: any) => {
          handlePaymentFailure(error);
        }
      };

      // Add mobile-specific configuration
      if (isMobile) {
        Object.assign(paymentOptions, getMobilePaymentConfig());
      }

      // Open Razorpay checkout
      console.log('🎯 Opening payment gateway...');
      await openRazorpayCheckout(razorpayOrder, paymentOptions);

    } catch (error) {
      console.error('❌ Payment process error:', error);
      setIsLoading(false);
      
      const errorMessage = error instanceof Error ? error.message : 'Payment failed';
      toast.error(errorMessage);
      
      if (options.onFailure) {
        options.onFailure(error);
      }
    }
  };

  /**
   * Handle successful payment
   */
  const handlePaymentSuccess = async (response: PaymentResponse, tempPaymentData: any) => {
    try {
      console.log('✅ Payment successful, verifying...');

      // Verify payment with backend
      const isVerified = await verifyRazorpayPayment({
        razorpay_payment_id: response.razorpay_payment_id,
        razorpay_order_id: response.razorpay_order_id,
        razorpay_signature: response.razorpay_signature
      });

      if (!isVerified) {
        throw new Error('Payment verification failed');
      }

      console.log('🎉 Payment verified successfully');

      // NOW create the order in database after successful payment
      console.log('📦 Creating order in database after payment confirmation...');
      const order = await createOrderAfterPayment(
        user.id,
        tempPaymentData.cartItems,
        tempPaymentData.amount,
        'online',
        tempPaymentData.shippingAddress,
        tempPaymentData.billingAddress,
        {
          shippingFee: tempPaymentData.shippingFee || 0,
          shippingNotes: tempPaymentData.notes || ''
        },
        response.razorpay_payment_id,
        response.razorpay_order_id
      );

      if (!order) {
        throw new Error('Failed to create order after payment');
      }

      console.log('✅ Order created after payment:', order.id);

      // Clear cart
      clearCart(false);

      // Show success message
      toast.success('Payment successful! Your order has been placed.');

      // Call success callback
      if (options.onSuccess) {
        options.onSuccess(order.id, response.razorpay_payment_id);
      }

      // Navigate to success page
      navigate(`/payment-success?order_id=${order.id}&payment_id=${response.razorpay_payment_id}`);

    } catch (error) {
      console.error('❌ Payment success handling error:', error);
      handlePaymentFailure(error);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handle payment failure
   */
  const handlePaymentFailure = (error: any) => {
    console.error('❌ Payment failed:', error);
    setIsLoading(false);

    const errorMessage = error instanceof Error ? error.message : 'Payment failed';
    toast.error(errorMessage);

    if (options.onFailure) {
      options.onFailure(error);
    }

    // Navigate to failure page
    navigate(`/payment-failure?error=${encodeURIComponent(errorMessage)}`);
  };



  return {
    processPayment,
    isLoading
  };
}
