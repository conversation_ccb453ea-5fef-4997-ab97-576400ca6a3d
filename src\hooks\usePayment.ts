/**
 * Payment Hook
 * Handles Razorpay payment processing
 */
import { useState } from 'react';
import { toast } from 'sonner';
import { useAuth } from '@/context/SupabaseAuthContext';
import { Order } from '@/services/orderService';
import { 
  createRazorpayOrder, 
  verifyRazorpayPayment, 
  loadRazorpayScript,
  PaymentResponse 
} from '@/services/payment/razorpayService';

interface UsePaymentOptions {
  onSuccess?: (paymentId: string, orderId: string) => void;
  onFailure?: (error: any) => void;
}

export function usePayment(options: UsePaymentOptions = {}) {
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuth();

  /**
   * Process payment using Razorpay
   */
  const processPayment = async (order: Order) => {
    if (!user) {
      toast.error('You need to be logged in to make a payment.');
      return;
    }

    setIsLoading(true);

    try {
      // Load Razorpay script
      const scriptLoaded = await loadRazorpayScript();
      if (!scriptLoaded) {
        throw new Error('Failed to load payment gateway');
      }

      // Validate environment variables
      if (!import.meta.env.VITE_RAZORPAY_KEY_ID) {
        throw new Error('Missing VITE_RAZORPAY_KEY_ID environment variable');
      }

      // Create Razorpay order
      const razorpayOrder = await createRazorpayOrder(
        order.total_amount,
        'INR',
        order.id,
        {
          user_id: order.user_id,
          email: user.email,
          shipping_address: order.shipping_address
        }
      );

      // Detect mobile device
      const isMobile = window.innerWidth <= 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

      // Configure Razorpay options
      const options: any = {
        key: import.meta.env.VITE_RAZORPAY_KEY_ID,
        amount: razorpayOrder.amount,
        currency: razorpayOrder.currency,
        name: 'The Badhees',
        description: `Order #${order.id.substring(0, 8)}`,
        order_id: razorpayOrder.id,
        prefill: {
          name: user.displayName || user.name || '',
          email: user.email,
          contact: '',
        },
        notes: {
          order_id: order.id,
          shipping_address: JSON.stringify(order.shipping_address),
        },
        theme: {
          color: '#3B82F6',
        },
        handler: function (response: PaymentResponse) {
          if (isMobile) {
            handleMobilePaymentSuccess(response, order);
          } else {
            handlePaymentSuccess(response, order);
          }
        },
        modal: {
          ondismiss: function () {
            if (isMobile) {
              handleMobilePaymentDismissal(order);
            } else {
              setIsLoading(false);
              toast.info('Payment cancelled. You can try again later.');
            }
          },
        },
      };

      // Add mobile-specific options
      if (isMobile) {
        console.log('📱 Mobile device detected - configuring for mobile payments');

        // Store order details for mobile redirect handling
        if (order.id.startsWith('temp_')) {
          // For temporary orders, store all necessary data
          sessionStorage.setItem('pendingPaymentOrder', JSON.stringify({
            orderId: order.id,
            razorpayOrderId: razorpayOrder.id,
            amount: order.total_amount,
            shippingFee: order.shipping_fee,
            shippingNotes: order.shipping_notes,
            timestamp: Date.now()
          }));

          // Store cart items and addresses for order creation
          sessionStorage.setItem('checkoutCart', JSON.stringify(order.items || []));
          sessionStorage.setItem('checkoutAddress', JSON.stringify({
            shipping: order.shipping_address,
            billing: order.billing_address
          }));

          // For mobile devices, also store in MobilePaymentService
          if (isMobile) {
            const { default: MobilePaymentService } = await import('@/services/mobilePaymentService');
            MobilePaymentService.storePaymentData({
              orderId: order.id,
              razorpayOrderId: razorpayOrder.id,
              amount: order.total_amount,
              userId: user.id,
              timestamp: Date.now(),
              status: 'initiated',
              orderData: {
                items: order.items || [],
                shipping_address: order.shipping_address,
                billing_address: order.billing_address,
                shipping_fee: order.shipping_fee || 0,
                payment_method: 'Online Payment'
              }
            });
          }
        }

        // Get the correct URL for redirects
        const isDevelopment = window.location.hostname === 'localhost' ||
                             window.location.hostname === '127.0.0.1' ||
                             window.location.hostname.includes('192.168');

        let currentUrl: string;
        if (isDevelopment) {
          // For development, use the current protocol and hostname with the current port
          const protocol = window.location.protocol;
          const hostname = window.location.hostname;
          const currentPort = window.location.port;
          currentUrl = currentPort ? `${protocol}//${hostname}:${currentPort}` : `${protocol}//${hostname}`;
        } else {
          // For production, use the full origin
          currentUrl = window.location.origin;
        }

        // Configure callback URLs for proper redirects
        options.callback_url = `${currentUrl}/payment-success?razorpay_payment_id={razorpay_payment_id}&razorpay_order_id={razorpay_order_id}&razorpay_signature={razorpay_signature}&order_id=${order.id}`;
        options.cancel_url = `${currentUrl}/payment-failure?order_id=${order.id}`;
        options.redirect = true;

        // Add retry mechanism for mobile redirects
        options.retry = {
          enabled: true,
          max_count: 3
        };

        // Configure for mobile experience - show all payment options
        options.config = {
          display: {
            preferences: {
              show_default_blocks: true // Show all payment options on mobile
            }
          }
        };

        console.log('📱 Mobile redirect URLs configured:', {
          callback_url: options.callback_url,
          cancel_url: options.cancel_url,
          current_url: currentUrl,
          is_development: isDevelopment,
          hostname: window.location.hostname,
          port: window.location.port
        });
      }

      // Open Razorpay checkout
      const razorpay = new window.Razorpay(options);
      razorpay.open();

    } catch (error) {
      console.error('Error initializing payment:', error);
      setIsLoading(false);
      toast.error('Failed to initialize payment. Please try again.');

      if (options.onFailure) {
        options.onFailure(error);
      }
    }
  };

  /**
   * Handle mobile payment success
   */
  const handleMobilePaymentSuccess = async (response: PaymentResponse, order: Order) => {
    try {
      console.log('📱 Mobile payment successful, redirecting to verification...');

      // Import MobilePaymentService
      const { default: MobilePaymentService } = await import('@/services/mobilePaymentService');

      // Update stored payment data with payment response
      MobilePaymentService.updatePaymentData({
        razorpayPaymentId: response.razorpay_payment_id,
        razorpaySignature: response.razorpay_signature,
        status: 'completed',
        completedAt: Date.now()
      });

      // Store payment response for verification (fallback)
      sessionStorage.setItem('mobilePaymentResponse', JSON.stringify({
        razorpay_payment_id: response.razorpay_payment_id,
        razorpay_order_id: response.razorpay_order_id,
        razorpay_signature: response.razorpay_signature,
        order_id: order.id,
        timestamp: Date.now()
      }));

      // Redirect to verification page with payment data
      const verificationUrl = `/payment-verify?razorpay_payment_id=${response.razorpay_payment_id}&razorpay_order_id=${response.razorpay_order_id}&razorpay_signature=${response.razorpay_signature}&order_id=${order.id}`;
      window.location.href = verificationUrl;

    } catch (error) {
      console.error('❌ Error in mobile payment success:', error);
      setIsLoading(false);
      toast.error('Payment completed but verification failed. Please contact support.');
    }
  };

  /**
   * Handle mobile payment dismissal
   */
  const handleMobilePaymentDismissal = async (order: Order) => {
    console.log('📱 Mobile payment dismissed, redirecting to verification...');
    setIsLoading(false);

    try {
      // Import MobilePaymentService
      const { default: MobilePaymentService } = await import('@/services/mobilePaymentService');

      // Update payment data status to dismissed
      MobilePaymentService.updatePaymentData({
        status: 'dismissed',
        dismissedAt: Date.now()
      });

      // Redirect to verification page to check for payment
      setTimeout(() => {
        window.location.href = `/payment-verify?dismissed=true&order_id=${order.id}`;
      }, 1000);
    } catch (error) {
      console.error('❌ Error handling payment dismissal:', error);
      // Still redirect to verification page
      setTimeout(() => {
        window.location.href = `/payment-verify?dismissed=true&order_id=${order.id}`;
      }, 1000);
    }
  };

  /**
   * Handle successful payment
   */
  const handlePaymentSuccess = async (response: PaymentResponse, order: Order) => {
    try {
      console.log('💻 Desktop payment successful, processing...');

      // For desktop, redirect to payment success page with all parameters
      const successUrl = `/payment-success?razorpay_payment_id=${response.razorpay_payment_id}&razorpay_order_id=${response.razorpay_order_id}&razorpay_signature=${response.razorpay_signature}&order_id=${order.id}`;
      window.location.href = successUrl;

    } catch (error) {
      console.error('Error handling desktop payment success:', error);
      toast.error('Payment completed but processing failed. Please contact support.');

      if (options.onFailure) {
        options.onFailure(error);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return {
    processPayment,
    isLoading,
  };
}
