/**
 * Payment Hook
 * Handles Razorpay payment processing
 */
import { useState } from 'react';
import { toast } from 'sonner';
import { useAuth } from '@/context/SupabaseAuthContext';
import { Order } from '@/services/orderService';
import { 
  createRazorpayOrder, 
  verifyRazorpayPayment, 
  loadRazorpayScript,
  PaymentResponse 
} from '@/services/payment/razorpayService';

interface UsePaymentOptions {
  onSuccess?: (paymentId: string, orderId: string) => void;
  onFailure?: (error: any) => void;
}

export function usePayment(options: UsePaymentOptions = {}) {
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuth();

  /**
   * Process payment using Razorpay
   */
  const processPayment = async (order: Order) => {
    if (!user) {
      toast.error('You need to be logged in to make a payment.');
      return;
    }

    setIsLoading(true);

    try {
      // Load Razorpay script
      const scriptLoaded = await loadRazorpayScript();
      if (!scriptLoaded) {
        throw new Error('Failed to load payment gateway');
      }

      // Validate environment variables
      if (!import.meta.env.VITE_RAZORPAY_KEY_ID) {
        throw new Error('Missing VITE_RAZORPAY_KEY_ID environment variable');
      }

      // Create Razorpay order
      const razorpayOrder = await createRazorpayOrder(
        order.total_amount,
        'INR',
        order.id,
        {
          user_id: order.user_id,
          email: user.email,
          shipping_address: order.shipping_address
        }
      );

      // Detect mobile device
      const isMobile = window.innerWidth <= 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

      // Configure Razorpay options
      const options: any = {
        key: import.meta.env.VITE_RAZORPAY_KEY_ID,
        amount: razorpayOrder.amount,
        currency: razorpayOrder.currency,
        name: 'The Badhees',
        description: `Order #${order.id.substring(0, 8)}`,
        order_id: razorpayOrder.id,
        prefill: {
          name: user.displayName || user.name || '',
          email: user.email,
          contact: '',
        },
        notes: {
          order_id: order.id,
          shipping_address: JSON.stringify(order.shipping_address),
        },
        theme: {
          color: '#3B82F6',
        },
        handler: function (response: PaymentResponse) {
          if (isMobile) {
            handleMobilePaymentSuccess(response, order);
          } else {
            handlePaymentSuccess(response, order);
          }
        },
        modal: {
          ondismiss: function () {
            if (isMobile) {
              handleMobilePaymentDismissal(order);
            } else {
              setIsLoading(false);
              toast.info('Payment cancelled. You can try again later.');
            }
          },
        },
      };

      // Add mobile-specific options
      if (isMobile) {
        console.log('📱 Mobile device detected - configuring for mobile payments');

        // Store order details for mobile redirect handling
        if (order.id.startsWith('temp_')) {
          // For temporary orders, store all necessary data
          sessionStorage.setItem('pendingPaymentOrder', JSON.stringify({
            orderId: order.id,
            razorpayOrderId: razorpayOrder.id,
            amount: order.total_amount,
            shippingFee: order.shipping_fee,
            shippingNotes: order.shipping_notes,
            timestamp: Date.now()
          }));

          // Store cart items and addresses for order creation
          sessionStorage.setItem('checkoutCart', JSON.stringify(order.order_items || []));
          sessionStorage.setItem('checkoutAddress', JSON.stringify({
            shipping: order.shipping_address,
            billing: order.billing_address
          }));
        }

        // Get the correct URL for mobile redirects
        const isDevelopment = window.location.hostname === 'localhost' ||
                             window.location.hostname === '127.0.0.1' ||
                             window.location.hostname.includes('192.168');

        let currentUrl: string;
        if (isDevelopment) {
          // For development, use the current protocol and hostname with the current port
          const protocol = window.location.protocol;
          const hostname = window.location.hostname;
          const currentPort = window.location.port;
          currentUrl = currentPort ? `${protocol}//${hostname}:${currentPort}` : `${protocol}//${hostname}`;
        } else {
          // For production, use the full origin
          currentUrl = window.location.origin;
        }

        // Remove redirect and callback_url to prevent 405 errors
        // Let the handler function manage the mobile payment flow
        options.redirect = false;

        // Add retry mechanism for mobile redirects
        options.retry = {
          enabled: true,
          max_count: 3
        };

        // Configure for better mobile UPI experience
        options.config = {
          display: {
            blocks: {
              banks: {
                name: 'Pay using UPI',
                instruments: [
                  {
                    method: 'upi'
                  }
                ]
              }
            },
            sequence: ['block.banks'],
            preferences: {
              show_default_blocks: false
            }
          }
        };

        console.log('📱 Mobile redirect URLs configured:', {
          callback_url: options.callback_url,
          cancel_url: options.cancel_url,
          current_url: currentUrl,
          is_development: isDevelopment,
          hostname: window.location.hostname,
          port: window.location.port
        });
      }

      // Open Razorpay checkout
      const razorpay = new window.Razorpay(options);
      razorpay.open();

    } catch (error) {
      console.error('Error initializing payment:', error);
      setIsLoading(false);
      toast.error('Failed to initialize payment. Please try again.');

      if (options.onFailure) {
        options.onFailure(error);
      }
    }
  };

  /**
   * Handle mobile payment success
   */
  const handleMobilePaymentSuccess = async (response: PaymentResponse, order: Order) => {
    try {
      console.log('📱 Mobile payment successful, redirecting to verification...');

      // Store payment response for verification
      sessionStorage.setItem('mobilePaymentResponse', JSON.stringify({
        razorpay_payment_id: response.razorpay_payment_id,
        razorpay_order_id: response.razorpay_order_id,
        razorpay_signature: response.razorpay_signature,
        order_id: order.id,
        timestamp: Date.now()
      }));

      // Redirect to verification page with payment data
      const verificationUrl = `/payment-verify?razorpay_payment_id=${response.razorpay_payment_id}&razorpay_order_id=${response.razorpay_order_id}&razorpay_signature=${response.razorpay_signature}&order_id=${order.id}`;
      window.location.href = verificationUrl;

    } catch (error) {
      console.error('❌ Error in mobile payment success:', error);
      setIsLoading(false);
      toast.error('Payment completed but verification failed. Please contact support.');
    }
  };

  /**
   * Handle mobile payment dismissal
   */
  const handleMobilePaymentDismissal = (order: Order) => {
    console.log('📱 Mobile payment dismissed, redirecting to verification...');
    setIsLoading(false);

    // Redirect to verification page to check for payment
    setTimeout(() => {
      window.location.href = `/payment-verify?dismissed=true&order_id=${order.id}`;
    }, 1000);
  };

  /**
   * Handle successful payment
   */
  const handlePaymentSuccess = async (response: PaymentResponse, order: Order) => {
    try {
      // Verify payment
      const isVerified = await verifyRazorpayPayment(response, order.id);

      if (isVerified) {
        console.log('✅ Payment verified successfully');
        
        if (options.onSuccess) {
          options.onSuccess(response.razorpay_payment_id, order.id);
        }
      } else {
        throw new Error('Payment verification failed');
      }
    } catch (error) {
      console.error('Error verifying payment:', error);
      toast.error('Payment verification failed. Please contact support if money was deducted.');

      if (options.onFailure) {
        options.onFailure(error);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return {
    processPayment,
    isLoading,
  };
}
