/**
 * Payment Hook
 * Handles Razorpay payment processing with proper error handling and order creation
 */

import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { useAuth } from '@/context/SupabaseAuthContext';
import { useCart } from '@/context/SupabaseCartContext';
import { 
  createRazorpayOrder, 
  verifyRazorpayPayment, 
  openRazorpayCheckout,
  isMobileDevice,
  getMobilePaymentConfig,
  PaymentResponse,
  CreateOrderRequest
} from '@/services/paymentService';
import { createOrder, createOrderAfterPayment, Order } from '@/services/orderService';

interface UsePaymentOptions {
  onSuccess?: (orderId: string, paymentId: string) => void;
  onFailure?: (error: any) => void;
}

interface PaymentData {
  amount: number;
  cartItems: any[];
  shippingAddress: any;
  billingAddress?: any;
  shippingFee?: number;
  notes?: string;
}

export function usePayment(options: UsePaymentOptions = {}) {
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuth();
  const { clearCart } = useCart();
  const navigate = useNavigate();

  /**
   * Process payment for the given order data
   */
  const processPayment = async (paymentData: PaymentData) => {
    if (!user) {
      toast.error('You need to be logged in to make a payment.');
      return;
    }

    setIsLoading(true);

    try {
      console.log('🚀 Starting payment process...');

      // Validate environment variables
      const razorpayKeyId = import.meta.env.VITE_RAZORPAY_KEY_ID;
      if (!razorpayKeyId) {
        throw new Error('Payment gateway not configured');
      }

      // Generate a temporary receipt ID for Razorpay order
      const tempReceiptId = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;

      // Create Razorpay order FIRST (without database order)
      console.log('💳 Creating Razorpay order...');
      const razorpayOrderData: CreateOrderRequest = {
        amount: paymentData.amount,
        currency: 'INR',
        receipt: tempReceiptId.substring(0, 39), // Keep under 40 chars
        notes: {
          user_id: user.id,
          user_email: user.email,
          item_count: paymentData.cartItems.length,
          cart_data: JSON.stringify(paymentData.cartItems.map(item => ({
            product_id: item.product.id,
            quantity: item.quantity,
            price: item.product.isSale && item.product.salePrice ? item.product.salePrice : item.product.price
          })))
        }
      };

      const razorpayOrder = await createRazorpayOrder(razorpayOrderData);

      // Store payment data temporarily for order creation after payment success
      const tempPaymentData = {
        ...paymentData,
        razorpayOrderId: razorpayOrder.id,
        userId: user.id,
        userEmail: user.email
      };

      // Configure payment options
      const isMobile = isMobileDevice();

      // For mobile devices, store payment data in localStorage to survive app redirections
      if (isMobile) {
        console.log('📱 Mobile device detected - storing payment data in localStorage');
        localStorage.setItem('mobile_payment_data', JSON.stringify({
          ...tempPaymentData,
          timestamp: Date.now(),
          razorpayOrderId: razorpayOrder.id
        }));
      }
      const paymentOptions = {
        key: razorpayKeyId,
        name: 'The Badhees',
        description: `Payment for ₹${paymentData.amount}`,
        prefill: {
          name: user.displayName || user.name || '',
          email: user.email || '',
          contact: paymentData.shippingAddress?.phone || ''
        },
        notes: {
          user_id: user.id
        },
        theme: {
          color: '#3B82F6'
        },
        onSuccess: (response: PaymentResponse) => {
          handlePaymentSuccess(response, tempPaymentData);
        },
        onFailure: (error: any) => {
          handlePaymentFailure(error);
        }
      };

      // Add mobile-specific configuration
      if (isMobile) {
        Object.assign(paymentOptions, getMobilePaymentConfig());
      }

      // Open Razorpay checkout
      console.log('🎯 Opening payment gateway...');
      await openRazorpayCheckout(razorpayOrder, paymentOptions);

    } catch (error) {
      console.error('❌ Payment process error:', error);
      setIsLoading(false);
      
      const errorMessage = error instanceof Error ? error.message : 'Payment failed';
      toast.error(errorMessage);
      
      if (options.onFailure) {
        options.onFailure(error);
      }
    }
  };

  /**
   * Handle successful payment
   */
  const handlePaymentSuccess = async (response: PaymentResponse, tempPaymentData: any) => {
    try {
      console.log('✅ Payment successful, verifying...');

      // For mobile devices, try to recover payment data from localStorage if tempPaymentData is incomplete
      let paymentData = tempPaymentData;
      const isMobile = isMobileDevice();

      if (isMobile && (!paymentData || !paymentData.cartItems || paymentData.cartItems.length === 0)) {
        console.log('📱 Mobile device - attempting to recover payment data from localStorage');
        const storedData = localStorage.getItem('mobile_payment_data');
        if (storedData) {
          try {
            const parsedData = JSON.parse(storedData);
            // Check if data is not too old (30 minutes)
            const maxAge = 30 * 60 * 1000; // 30 minutes
            if (Date.now() - parsedData.timestamp < maxAge) {
              console.log('✅ Successfully recovered payment data from localStorage');
              paymentData = parsedData;
            } else {
              console.log('⚠️ Stored payment data is too old, removing...');
              localStorage.removeItem('mobile_payment_data');
            }
          } catch (e) {
            console.error('❌ Error parsing stored payment data:', e);
            localStorage.removeItem('mobile_payment_data');
          }
        }
      }

      // Verify payment with backend
      const isVerified = await verifyRazorpayPayment({
        razorpay_payment_id: response.razorpay_payment_id,
        razorpay_order_id: response.razorpay_order_id,
        razorpay_signature: response.razorpay_signature
      });

      if (!isVerified) {
        throw new Error('Payment verification failed');
      }

      console.log('🎉 Payment verified successfully');

      // Validate that we have the necessary data for order creation
      if (!paymentData || !paymentData.cartItems || paymentData.cartItems.length === 0) {
        throw new Error('Payment data is missing or incomplete. Cannot create order.');
      }

      // NOW create the order in database after successful payment
      console.log('📦 Creating order in database after payment confirmation...');
      console.log('📋 Order creation data:', {
        userId: user.id,
        cartItemsCount: paymentData.cartItems?.length || 0,
        amount: paymentData.amount,
        paymentMethod: 'online',
        razorpayPaymentId: response.razorpay_payment_id,
        razorpayOrderId: response.razorpay_order_id,
        hasShippingAddress: !!paymentData.shippingAddress,
        hasBillingAddress: !!paymentData.billingAddress
      });

      // Add timeout wrapper for order creation
      const orderCreationPromise = createOrderAfterPayment(
        user.id,
        paymentData.cartItems,
        paymentData.amount,
        'online',
        paymentData.shippingAddress,
        paymentData.billingAddress,
        {
          shippingFee: paymentData.shippingFee || 0,
          shippingNotes: paymentData.notes || ''
        },
        response.razorpay_payment_id,
        response.razorpay_order_id
      );

      const orderTimeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Order creation timeout after 60 seconds')), 60000);
      });

      console.log('⏱️ Starting order creation with 60-second timeout...');
      const order = await Promise.race([
        orderCreationPromise,
        orderTimeoutPromise
      ]) as any;

      if (!order) {
        throw new Error('Failed to create order after payment');
      }

      console.log('✅ Order created after payment:', order.id);

      // Clear cart
      clearCart(false);

      // Clean up mobile payment data from localStorage
      if (isMobile) {
        console.log('🧹 Cleaning up mobile payment data from localStorage');
        localStorage.removeItem('mobile_payment_data');
      }

      // Show success message
      toast.success('Payment successful! Your order has been placed.');

      // Call success callback
      if (options.onSuccess) {
        options.onSuccess(order.id, response.razorpay_payment_id);
      }

      // Navigate to success page
      navigate(`/payment-success?order_id=${order.id}&payment_id=${response.razorpay_payment_id}`);

    } catch (error) {
      console.error('❌ Payment success handling error:', error);
      handlePaymentFailure(error);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handle payment failure
   */
  const handlePaymentFailure = (error: any) => {
    console.error('❌ Payment failed:', error);
    setIsLoading(false);

    // Clean up mobile payment data from localStorage on failure
    const isMobile = isMobileDevice();
    if (isMobile) {
      console.log('🧹 Cleaning up mobile payment data from localStorage (failure)');
      localStorage.removeItem('mobile_payment_data');
    }

    const errorMessage = error instanceof Error ? error.message : 'Payment failed';
    toast.error(errorMessage);

    if (options.onFailure) {
      options.onFailure(error);
    }

    // Navigate to failure page
    navigate(`/payment-failure?error=${encodeURIComponent(errorMessage)}`);
  };



  return {
    processPayment,
    isLoading
  };
}
