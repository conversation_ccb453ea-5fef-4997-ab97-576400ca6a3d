/**
 * Payment Hook
 * Handles Razorpay payment processing with proper error handling and order creation
 */

import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { useAuth } from '@/context/SupabaseAuthContext';
import { useCart } from '@/context/SupabaseCartContext';
import { 
  createRazorpayOrder, 
  verifyRazorpayPayment, 
  openRazorpayCheckout,
  isMobileDevice,
  getMobilePaymentConfig,
  PaymentResponse,
  CreateOrderRequest
} from '@/services/paymentService';
import { createOrder, Order } from '@/services/orderService';

interface UsePaymentOptions {
  onSuccess?: (orderId: string, paymentId: string) => void;
  onFailure?: (error: any) => void;
}

interface PaymentData {
  amount: number;
  cartItems: any[];
  shippingAddress: any;
  billingAddress?: any;
  shippingFee?: number;
  notes?: string;
}

export function usePayment(options: UsePaymentOptions = {}) {
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuth();
  const { clearCart } = useCart();
  const navigate = useNavigate();

  /**
   * Process payment for the given order data
   */
  const processPayment = async (paymentData: PaymentData) => {
    if (!user) {
      toast.error('You need to be logged in to make a payment.');
      return;
    }

    setIsLoading(true);

    try {
      console.log('🚀 Starting payment process...');

      // Validate environment variables
      const razorpayKeyId = import.meta.env.VITE_RAZORPAY_KEY_ID;
      if (!razorpayKeyId) {
        throw new Error('Payment gateway not configured');
      }

      // Create order in database first
      console.log('📦 Creating order in database...');
      const order = await createOrder(
        user.id,
        paymentData.cartItems,
        paymentData.amount,
        'online',
        paymentData.shippingAddress,
        paymentData.billingAddress,
        {
          shippingFee: paymentData.shippingFee || 0,
          shippingNotes: paymentData.notes || ''
        }
      );

      if (!order) {
        throw new Error('Failed to create order');
      }

      console.log('✅ Order created:', order.id);

      // Create Razorpay order
      console.log('💳 Creating Razorpay order...');
      const razorpayOrderData: CreateOrderRequest = {
        amount: paymentData.amount,
        currency: 'INR',
        receipt: `order_${order.id}`,
        notes: {
          order_id: order.id,
          user_id: user.id,
          user_email: user.email,
          item_count: paymentData.cartItems.length
        }
      };

      const razorpayOrder = await createRazorpayOrder(razorpayOrderData);

      // Store payment record in database
      console.log('💾 Storing payment record...');
      await storePaymentRecord(order.id, razorpayOrder.id, paymentData.amount);

      // Configure payment options
      const isMobile = isMobileDevice();
      const paymentOptions = {
        key: razorpayKeyId,
        name: 'The Badhees',
        description: `Order #${order.id.substring(0, 8)}`,
        prefill: {
          name: user.displayName || user.name || '',
          email: user.email || '',
          contact: paymentData.shippingAddress?.phone || ''
        },
        notes: {
          order_id: order.id,
          user_id: user.id
        },
        theme: {
          color: '#3B82F6'
        },
        onSuccess: (response: PaymentResponse) => {
          handlePaymentSuccess(response, order.id);
        },
        onFailure: (error: any) => {
          handlePaymentFailure(error, order.id);
        }
      };

      // Add mobile-specific configuration
      if (isMobile) {
        Object.assign(paymentOptions, getMobilePaymentConfig());
      }

      // Open Razorpay checkout
      console.log('🎯 Opening payment gateway...');
      await openRazorpayCheckout(razorpayOrder, paymentOptions);

    } catch (error) {
      console.error('❌ Payment process error:', error);
      setIsLoading(false);
      
      const errorMessage = error instanceof Error ? error.message : 'Payment failed';
      toast.error(errorMessage);
      
      if (options.onFailure) {
        options.onFailure(error);
      }
    }
  };

  /**
   * Handle successful payment
   */
  const handlePaymentSuccess = async (response: PaymentResponse, orderId: string) => {
    try {
      console.log('✅ Payment successful, verifying...');

      // Verify payment with backend
      const isVerified = await verifyRazorpayPayment({
        razorpay_payment_id: response.razorpay_payment_id,
        razorpay_order_id: response.razorpay_order_id,
        razorpay_signature: response.razorpay_signature,
        order_id: orderId
      });

      if (!isVerified) {
        throw new Error('Payment verification failed');
      }

      console.log('🎉 Payment verified successfully');

      // Clear cart
      clearCart(false);

      // Show success message
      toast.success('Payment successful! Your order has been placed.');

      // Call success callback
      if (options.onSuccess) {
        options.onSuccess(orderId, response.razorpay_payment_id);
      }

      // Navigate to success page
      navigate(`/payment-success?order_id=${orderId}&payment_id=${response.razorpay_payment_id}`);

    } catch (error) {
      console.error('❌ Payment success handling error:', error);
      handlePaymentFailure(error, orderId);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handle payment failure
   */
  const handlePaymentFailure = (error: any, orderId?: string) => {
    console.error('❌ Payment failed:', error);
    setIsLoading(false);

    const errorMessage = error instanceof Error ? error.message : 'Payment failed';
    toast.error(errorMessage);

    if (options.onFailure) {
      options.onFailure(error);
    }

    // Navigate to failure page with order ID for retry option
    if (orderId) {
      navigate(`/payment-failure?order_id=${orderId}&error=${encodeURIComponent(errorMessage)}`);
    }
  };

  /**
   * Store payment record in database
   */
  const storePaymentRecord = async (orderId: string, razorpayOrderId: string, amount: number) => {
    try {
      // This would typically be done via a Supabase function or API call
      // For now, we'll rely on the webhook to create the payment record
      console.log('💾 Payment record will be created by webhook');
    } catch (error) {
      console.error('❌ Error storing payment record:', error);
      // Don't fail the payment process for this
    }
  };

  return {
    processPayment,
    isLoading
  };
}
