/**
 * Failed Order Recovery Component
 * Handles recovery of orders that failed to create after successful payment
 */

import React, { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { useAuth } from '@/context/SupabaseAuthContext';
import { createOrderAfterPayment } from '@/services/orderService';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertTriangle, RefreshCw } from 'lucide-react';

interface FailedOrderData {
  userId: string;
  cartItems: any[];
  totalAmount: number;
  paymentMethod: string;
  shippingAddress?: any;
  billingAddress?: any;
  shippingInfo?: any;
  razorpayPaymentId: string;
  razorpayOrderId: string;
  timestamp: string;
  error: string;
}

export const FailedOrderRecovery: React.FC = () => {
  const { user } = useAuth();
  const [failedOrder, setFailedOrder] = useState<FailedOrderData | null>(null);
  const [isRecovering, setIsRecovering] = useState(false);

  useEffect(() => {
    // Check for failed order data in localStorage
    const checkForFailedOrder = () => {
      try {
        const failedOrderData = localStorage.getItem('failed_order_data');
        if (failedOrderData) {
          const parsedData: FailedOrderData = JSON.parse(failedOrderData);
          
          // Only show if it's for the current user and within last 24 hours
          const failedTime = new Date(parsedData.timestamp);
          const now = new Date();
          const hoursDiff = (now.getTime() - failedTime.getTime()) / (1000 * 60 * 60);
          
          if (parsedData.userId === user?.id && hoursDiff < 24) {
            setFailedOrder(parsedData);
            console.log('🔍 [FailedOrderRecovery] Found failed order data:', parsedData);
          } else {
            // Clean up old failed order data
            localStorage.removeItem('failed_order_data');
          }
        }
      } catch (error) {
        console.error('❌ [FailedOrderRecovery] Error checking failed order data:', error);
        localStorage.removeItem('failed_order_data');
      }
    };

    if (user) {
      checkForFailedOrder();
    }
  }, [user]);

  const handleRecoverOrder = async () => {
    if (!failedOrder || !user) return;

    setIsRecovering(true);
    console.log('🔄 [FailedOrderRecovery] Attempting to recover failed order...');

    try {
      const order = await createOrderAfterPayment(
        failedOrder.userId,
        failedOrder.cartItems,
        failedOrder.totalAmount,
        failedOrder.paymentMethod,
        failedOrder.shippingAddress,
        failedOrder.billingAddress,
        failedOrder.shippingInfo,
        failedOrder.razorpayPaymentId,
        failedOrder.razorpayOrderId
      );

      if (order) {
        console.log('✅ [FailedOrderRecovery] Order recovered successfully:', order.id);
        toast.success('Order recovered successfully! Your order has been placed.');
        
        // Clean up failed order data
        localStorage.removeItem('failed_order_data');
        setFailedOrder(null);
        
        // Redirect to order success page
        window.location.href = `/payment-success?order_id=${order.id}&payment_id=${failedOrder.razorpayPaymentId}`;
      } else {
        throw new Error('Order recovery failed');
      }
    } catch (error) {
      console.error('❌ [FailedOrderRecovery] Order recovery failed:', error);
      toast.error('Failed to recover order. Please contact support with your payment ID: ' + failedOrder.razorpayPaymentId);
    } finally {
      setIsRecovering(false);
    }
  };

  const handleDismiss = () => {
    localStorage.removeItem('failed_order_data');
    setFailedOrder(null);
    toast.info('Failed order data dismissed. Contact support if you need help.');
  };

  if (!failedOrder) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-500" />
            <CardTitle className="text-lg">Order Recovery Required</CardTitle>
          </div>
          <CardDescription>
            Your payment was successful but the order creation failed. We can try to recover your order now.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-sm space-y-2">
            <p><strong>Payment ID:</strong> {failedOrder.razorpayPaymentId}</p>
            <p><strong>Amount:</strong> ₹{failedOrder.totalAmount}</p>
            <p><strong>Items:</strong> {failedOrder.cartItems.length} item(s)</p>
            <p><strong>Failed at:</strong> {new Date(failedOrder.timestamp).toLocaleString()}</p>
          </div>
          
          <div className="flex gap-2">
            <Button 
              onClick={handleRecoverOrder} 
              disabled={isRecovering}
              className="flex-1"
            >
              {isRecovering ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Recovering...
                </>
              ) : (
                'Recover Order'
              )}
            </Button>
            <Button 
              variant="outline" 
              onClick={handleDismiss}
              disabled={isRecovering}
            >
              Dismiss
            </Button>
          </div>
          
          <p className="text-xs text-muted-foreground">
            If recovery fails, please contact support with your payment ID.
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

export default FailedOrderRecovery;
