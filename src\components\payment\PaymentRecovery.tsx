/**
 * Payment Recovery Component
 * 
 * This component allows users to retry failed payments or check payment status.
 */
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, AlertCircle, CheckCircle, RefreshCw } from 'lucide-react';
import { useAuth } from '@/context/SupabaseAuthContext';
import { supabase } from '@/lib/supabase';
import { usePayment } from '@/hooks/usePayment';
import { Order } from '@/services/orderService';

interface PaymentRecoveryProps {
  orderId: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

const PaymentRecovery: React.FC<PaymentRecoveryProps> = ({
  orderId,
  onSuccess,
  onCancel
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [order, setOrder] = useState<Order | null>(null);
  const [paymentStatus, setPaymentStatus] = useState<string | null>(null);
  const [paymentError, setPaymentError] = useState<string | null>(null);
  const { user } = useAuth();
  const navigate = useNavigate();
  const { processPayment } = usePayment({
    onSuccess: (orderId, paymentId) => {
      toast.success('Payment successful!');
      setPaymentStatus('paid');
      if (onSuccess) {
        onSuccess();
      } else {
        navigate(`/orders/${orderId}`);
      }
    },
    onFailure: (error) => {
      setPaymentError(error.message || 'Payment failed. Please try again.');
      setPaymentStatus('failed');
    }
  });

  useEffect(() => {
    const fetchOrderAndPaymentStatus = async () => {
      if (!orderId) {
        setIsLoading(false);
        return;
      }

      try {
        // Fetch order details
        const { data: orderData, error: orderError } = await supabase
          .from('orders')
          .select('*, shipping_address:shipping_addresses(*)')
          .eq('id', orderId)
          .single();

        if (orderError) throw orderError;
        
        if (!orderData) {
          throw new Error('Order not found');
        }

        setOrder(orderData as Order);

        // Fetch payment status
        const { data: paymentData, error: paymentError } = await supabase
          .from('payments')
          .select('*')
          .eq('order_id', orderId)
          .order('created_at', { ascending: false })
          .limit(1)
          .single();

        if (paymentError && paymentError.code !== 'PGRST116') {
          // PGRST116 is "no rows returned" error, which is fine if no payment exists
          console.error('Error fetching payment:', paymentError);
        }

        if (paymentData) {
          setPaymentStatus(paymentData.status);
          if (paymentData.error_description) {
            setPaymentError(paymentData.error_description);
          }
        } else {
          setPaymentStatus('not_started');
        }
      } catch (error) {
        console.error('Error fetching order or payment:', error);
        toast.error('Failed to load order details');
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrderAndPaymentStatus();
  }, [orderId]);

  const handleRetryPayment = async () => {
    if (!order) return;

    try {
      // Convert order to payment data format
      const paymentData = {
        amount: order.total_amount,
        cartItems: [], // This would need to be fetched from order_items
        shippingAddress: order.shipping_address,
        billingAddress: order.billing_address || order.shipping_address,
        shippingFee: order.shipping_fee || 0,
        notes: order.shipping_notes || ''
      };

      await processPayment(paymentData);
    } catch (error) {
      console.error('Error processing payment:', error);
      toast.error('Failed to initialize payment. Please try again.');
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      navigate('/orders');
    }
  };

  if (isLoading) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardContent className="pt-6 flex flex-col items-center justify-center min-h-[200px]">
          <Loader2 className="h-8 w-8 animate-spin text-badhees-600 mb-4" />
          <p className="text-center text-gray-600">Loading payment information...</p>
        </CardContent>
      </Card>
    );
  }

  if (!order) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardContent className="pt-6 flex flex-col items-center justify-center min-h-[200px]">
          <AlertCircle className="h-8 w-8 text-red-500 mb-4" />
          <p className="text-center text-gray-600">Order not found or you don't have permission to view it.</p>
          <Button onClick={() => navigate('/orders')} className="mt-4">
            View Your Orders
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Order Payment</CardTitle>
        <CardDescription>
          Order #{orderId.substring(0, 8)} - ₹{order.total_amount.toFixed(2)}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            {paymentStatus === 'paid' || paymentStatus === 'captured' ? (
              <CheckCircle className="h-6 w-6 text-green-500" />
            ) : paymentStatus === 'failed' ? (
              <AlertCircle className="h-6 w-6 text-red-500" />
            ) : (
              <RefreshCw className="h-6 w-6 text-badhees-600" />
            )}
            <div>
              <h3 className="font-medium">
                {paymentStatus === 'paid' || paymentStatus === 'captured'
                  ? 'Payment Successful'
                  : paymentStatus === 'failed'
                  ? 'Payment Failed'
                  : paymentStatus === 'created' || paymentStatus === 'authorized'
                  ? 'Payment Processing'
                  : 'Payment Not Started'}
              </h3>
              {paymentError && (
                <p className="text-sm text-red-600">{paymentError}</p>
              )}
            </div>
          </div>

          {(paymentStatus === 'failed' || paymentStatus === 'not_started') && (
            <div className="bg-badhees-50 p-4 rounded-md">
              <p className="text-sm text-gray-700 mb-2">
                You can retry the payment for this order. Your order will be processed once the payment is successful.
              </p>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={handleCancel}>
          {paymentStatus === 'paid' || paymentStatus === 'captured' ? 'Back to Order' : 'Cancel'}
        </Button>
        
        {(paymentStatus === 'failed' || paymentStatus === 'not_started') && (
          <Button onClick={handleRetryPayment}>
            Retry Payment
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

export default PaymentRecovery;
