/**
 * Payment API Server
 * Handles Razorpay payment processing with proper error handling and logging
 */

import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import <PERSON><PERSON>pay from 'razorpay';
import crypto from 'crypto';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3002;

// Initialize Supabase
const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY
);

// Initialize Razorpay
const razorpay = new Razorpay({
  key_id: process.env.RAZORPAY_KEY_ID,
  key_secret: process.env.RAZORPAY_SECRET,
});

// Middleware
app.use(cors({
  origin: ['http://localhost:8080', 'http://localhost:8081', 'http://localhost:3000', 'https://thebadhees.vercel.app'],
  credentials: true
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request logging middleware
app.use((req, res, next) => {
  console.log(`📝 ${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// API Routes

// Create Order Route
app.post('/api/create-order', async (req, res) => {
  try {
    console.log('🚀 [Create Order] Starting order creation...');

    // Validate environment variables
    if (!process.env.RAZORPAY_KEY_ID || !process.env.RAZORPAY_SECRET) {
      console.error('❌ [Create Order] Missing Razorpay credentials');
      return res.status(500).json({
        success: false,
        error: 'Payment gateway configuration error'
      });
    }

    // Extract request data
    const { amount, currency = 'INR', receipt, notes = {} } = req.body;

    // Validate required fields
    if (!amount || !receipt) {
      console.error('❌ [Create Order] Missing required fields');
      return res.status(400).json({
        success: false,
        error: 'Amount and receipt are required'
      });
    }

    // Validate amount
    if (isNaN(amount) || amount <= 0) {
      console.error('❌ [Create Order] Invalid amount:', amount);
      return res.status(400).json({
        success: false,
        error: 'Invalid amount provided'
      });
    }

    // Create Razorpay order
    const orderOptions = {
      amount: Math.round(amount * 100), // Convert to paise
      currency,
      receipt,
      notes,
      payment_capture: 1 // Auto capture payment
    };

    console.log('📝 [Create Order] Creating order with options:', {
      amount: orderOptions.amount,
      currency: orderOptions.currency,
      receipt: orderOptions.receipt,
      notesCount: Object.keys(notes).length
    });

    const order = await razorpay.orders.create(orderOptions);

    console.log('✅ [Create Order] Order created successfully:', {
      id: order.id,
      amount: order.amount,
      currency: order.currency,
      status: order.status
    });

    return res.status(200).json({
      success: true,
      data: {
        id: order.id,
        amount: order.amount,
        currency: order.currency,
        receipt: order.receipt,
        status: order.status,
        created_at: order.created_at
      }
    });

  } catch (error) {
    console.error('❌ [Create Order] Error:', error);

    return res.status(500).json({
      success: false,
      error: 'Failed to create payment order',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Verify Payment Route
app.post('/api/verify-payment', async (req, res) => {
  try {
    console.log('🔍 [Verify Payment] Starting payment verification...');

    const {
      razorpay_payment_id,
      razorpay_order_id,
      razorpay_signature,
      order_id
    } = req.body;

    // Validate required fields
    if (!razorpay_payment_id || !razorpay_order_id || !razorpay_signature || !order_id) {
      console.error('❌ [Verify Payment] Missing required fields');
      return res.status(400).json({
        success: false,
        error: 'Missing required payment verification fields'
      });
    }

    // Verify signature
    const body = razorpay_order_id + "|" + razorpay_payment_id;
    const expectedSignature = crypto
      .createHmac('sha256', process.env.RAZORPAY_SECRET)
      .update(body.toString())
      .digest('hex');

    const isAuthentic = expectedSignature === razorpay_signature;

    if (!isAuthentic) {
      console.error('❌ [Verify Payment] Invalid signature');
      return res.status(400).json({
        success: false,
        error: 'Invalid payment signature'
      });
    }

    console.log('✅ [Verify Payment] Signature verified successfully');

    // Update order status in database
    const { error: orderError } = await supabase
      .from('orders')
      .update({
        status: 'paid',
        payment_status: 'paid',
        razorpay_order_id,
        razorpay_payment_id,
        updated_at: new Date().toISOString()
      })
      .eq('id', order_id);

    if (orderError) {
      console.error('❌ [Verify Payment] Error updating order:', orderError);
      return res.status(500).json({
        success: false,
        error: 'Failed to update order status'
      });
    }

    // Update payment record
    const { error: paymentError } = await supabase
      .from('payments')
      .update({
        status: 'completed',
        razorpay_payment_id,
        updated_at: new Date().toISOString()
      })
      .eq('razorpay_order_id', razorpay_order_id);

    if (paymentError) {
      console.warn('⚠️ [Verify Payment] Error updating payment record:', paymentError);
    }

    console.log('✅ [Verify Payment] Payment verified and order updated successfully');

    return res.status(200).json({
      success: true,
      message: 'Payment verified successfully',
      data: {
        order_id,
        razorpay_payment_id,
        razorpay_order_id
      }
    });

  } catch (error) {
    console.error('❌ [Verify Payment] Error:', error);

    return res.status(500).json({
      success: false,
      error: 'Payment verification failed',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Webhook Route
app.post('/api/webhook', async (req, res) => {
  try {
    console.log('🔔 [Webhook] Received webhook');

    const webhookSignature = req.headers['x-razorpay-signature'];
    const webhookBody = JSON.stringify(req.body);

    // Verify webhook signature
    const expectedSignature = crypto
      .createHmac('sha256', process.env.RAZORPAY_WEBHOOK_SECRET)
      .update(webhookBody)
      .digest('hex');

    if (webhookSignature !== expectedSignature) {
      console.error('❌ [Webhook] Invalid signature');
      return res.status(400).json({
        success: false,
        error: 'Invalid webhook signature'
      });
    }

    const event = req.body;
    console.log('📨 [Webhook] Event type:', event.event);

    // Handle payment success
    if (event.event === 'payment.captured') {
      const payment = event.payload.payment.entity;

      console.log('💰 [Webhook] Payment captured:', {
        payment_id: payment.id,
        order_id: payment.order_id,
        amount: payment.amount,
        status: payment.status
      });

      // Update order status
      const { error: orderError } = await supabase
        .from('orders')
        .update({
          status: 'paid',
          payment_status: 'paid',
          razorpay_payment_id: payment.id,
          updated_at: new Date().toISOString()
        })
        .eq('razorpay_order_id', payment.order_id);

      if (orderError) {
        console.error('❌ [Webhook] Error updating order:', orderError);
      } else {
        console.log('✅ [Webhook] Order updated successfully');
      }

      // Update payment record
      const { error: paymentError } = await supabase
        .from('payments')
        .update({
          status: 'completed',
          razorpay_payment_id: payment.id,
          updated_at: new Date().toISOString()
        })
        .eq('razorpay_order_id', payment.order_id);

      if (paymentError) {
        console.warn('⚠️ [Webhook] Error updating payment record:', paymentError);
      }
    }

    return res.status(200).json({ success: true });

  } catch (error) {
    console.error('❌ [Webhook] Error:', error);
    return res.status(500).json({
      success: false,
      error: 'Webhook processing failed'
    });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    message: 'Payment API server is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'The Badhees Payment API',
    version: '2.0.0',
    endpoints: [
      'POST /api/create-order - Create Razorpay order',
      'POST /api/verify-payment - Verify payment signature',
      'POST /api/webhook - Handle Razorpay webhooks',
      'GET /api/health - Health check'
    ]
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('❌ Server Error:', error);
  
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found',
    path: req.originalUrl
  });
});

// Start server
const server = app.listen(PORT, () => {
  console.log('🚀 Payment API Server started');
  console.log(`📍 Server running on http://localhost:${PORT}`);
  console.log('📋 Available endpoints:');
  console.log('   POST /api/create-order');
  console.log('   POST /api/verify-payment');
  console.log('   POST /api/webhook');
  console.log('   GET  /api/health');

  // Environment check
  const hasRazorpayKey = !!process.env.RAZORPAY_KEY_ID;
  const hasRazorpaySecret = !!process.env.RAZORPAY_SECRET;
  const hasSupabaseUrl = !!process.env.VITE_SUPABASE_URL;

  console.log('🔍 Environment check:', {
    hasRazorpayKey,
    hasRazorpaySecret,
    hasSupabaseUrl,
    nodeEnv: process.env.NODE_ENV,
    port: PORT
  });

  if (!hasRazorpayKey || !hasRazorpaySecret) {
    console.warn('⚠️ Warning: Razorpay credentials not found in environment variables');
  }

  if (!hasSupabaseUrl) {
    console.warn('⚠️ Warning: Supabase URL not found in environment variables');
  }

  console.log('✅ Server is ready to accept connections');
});

// Keep the process alive
server.on('error', (error) => {
  console.error('❌ Server error:', error);
});

// Prevent the process from exiting
process.stdin.resume();

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down gracefully');
  process.exit(0);
});

export default app;
