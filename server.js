import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.VITE_SERVER_PORT || 3001;

// Middleware
app.use(cors({
  origin: ['http://localhost:8080', 'http://127.0.0.1:8080', 'http://localhost:3000'],
  credentials: true
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Import API routes
import createOrderHandler from './api/razorpay/create-order.js';
import verifyPaymentHandler from './api/razorpay/verify-payment.js';
import webhookHandler from './api/razorpay/webhook.js';
import checkPaymentStatusHandler from './api/razorpay/check-payment-status.js';

// API Routes
app.post('/api/razorpay/create-order', createOrderHandler);
app.post('/api/razorpay/verify-payment', verifyPaymentHandler);
app.post('/api/razorpay/webhook', webhookHandler);
app.post('/api/razorpay/check-payment-status', checkPaymentStatusHandler);

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'API server is running' });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 API Server running on http://localhost:${PORT}`);
  console.log(`📋 Available endpoints:`);
  console.log(`   POST /api/razorpay/create-order`);
  console.log(`   POST /api/razorpay/verify-payment`);
  console.log(`   POST /api/razorpay/webhook`);
  console.log(`   POST /api/razorpay/check-payment-status`);
  console.log(`   GET  /api/health`);
});
