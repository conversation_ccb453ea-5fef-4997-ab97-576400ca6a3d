/**
 * Payment API Server
 * Handles Razorpay payment processing with proper error handling and logging
 */

import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors({
  origin: ['http://localhost:8080', 'http://localhost:3000', 'https://thebadhees.vercel.app'],
  credentials: true
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request logging middleware
app.use((req, res, next) => {
  console.log(`📝 ${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Import API handlers
import createOrderHandler from './api/create-order.js';
import verifyPaymentHandler from './api/verify-payment.js';
import webhookHandler from './api/webhook.js';

// API Routes
app.post('/api/create-order', createOrderHandler);
app.post('/api/verify-payment', verifyPaymentHandler);
app.post('/api/webhook', webhookHandler);

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    message: 'Payment API server is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'The Badhees Payment API',
    version: '2.0.0',
    endpoints: [
      'POST /api/create-order - Create Razorpay order',
      'POST /api/verify-payment - Verify payment signature',
      'POST /api/webhook - Handle Razorpay webhooks',
      'GET /api/health - Health check'
    ]
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('❌ Server Error:', error);
  
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found',
    path: req.originalUrl
  });
});

// Start server
app.listen(PORT, () => {
  console.log('🚀 Payment API Server started');
  console.log(`📍 Server running on http://localhost:${PORT}`);
  console.log('📋 Available endpoints:');
  console.log('   POST /api/create-order');
  console.log('   POST /api/verify-payment');
  console.log('   POST /api/webhook');
  console.log('   GET  /api/health');
  
  // Environment check
  const hasRazorpayKey = !!process.env.RAZORPAY_KEY_ID;
  const hasRazorpaySecret = !!process.env.RAZORPAY_SECRET;
  const hasSupabaseUrl = !!process.env.VITE_SUPABASE_URL;
  
  console.log('🔍 Environment check:', {
    hasRazorpayKey,
    hasRazorpaySecret,
    hasSupabaseUrl,
    nodeEnv: process.env.NODE_ENV,
    port: PORT
  });
  
  if (!hasRazorpayKey || !hasRazorpaySecret) {
    console.warn('⚠️ Warning: Razorpay credentials not found in environment variables');
  }
  
  if (!hasSupabaseUrl) {
    console.warn('⚠️ Warning: Supabase URL not found in environment variables');
  }
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down gracefully');
  process.exit(0);
});

export default app;
