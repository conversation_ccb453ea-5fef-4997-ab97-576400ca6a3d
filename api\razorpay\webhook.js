import crypto from 'crypto';

export default async function handler(req, res) {
  // Set CORS headers
  const origin = req.headers.origin;
  
  if (origin && (
    origin.includes('thebadhees.com') ||
    origin.includes('localhost') ||
    origin.includes('127.0.0.1') ||
    origin.includes('vercel.app')
  )) {
    res.setHeader('Access-Control-Allow-Origin', origin);
  } else {
    res.setHeader('Access-Control-Allow-Origin', '*');
  }
  
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, X-Razorpay-Signature');
  res.setHeader('Access-Control-Allow-Credentials', 'true');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed. Only POST requests are accepted.' 
    });
  }

  try {
    console.log('🔔 [Webhook] Received Razorpay webhook');
    
    // Get webhook secret from environment
    const webhookSecret = process.env.RAZORPAY_WEBHOOK_SECRET;
    if (!webhookSecret) {
      console.error('❌ [Webhook] Missing RAZORPAY_WEBHOOK_SECRET environment variable');
      return res.status(500).json({
        success: false,
        error: 'Webhook secret not configured'
      });
    }

    // Get the signature from headers
    const receivedSignature = req.headers['x-razorpay-signature'];
    if (!receivedSignature) {
      console.error('❌ [Webhook] Missing X-Razorpay-Signature header');
      return res.status(400).json({
        success: false,
        error: 'Missing webhook signature'
      });
    }

    // Verify webhook signature
    const body = JSON.stringify(req.body);
    const expectedSignature = crypto
      .createHmac('sha256', webhookSecret)
      .update(body)
      .digest('hex');

    const isValidSignature = crypto.timingSafeEqual(
      Buffer.from(receivedSignature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );

    if (!isValidSignature) {
      console.error('❌ [Webhook] Invalid webhook signature');
      return res.status(400).json({
        success: false,
        error: 'Invalid webhook signature'
      });
    }

    console.log('✅ [Webhook] Signature verified successfully');

    // Process the webhook event
    const event = req.body.event;
    const payload = req.body.payload;

    console.log('📋 [Webhook] Event type:', event);
    console.log('📋 [Webhook] Payload:', JSON.stringify(payload, null, 2));

    // Handle different webhook events
    switch (event) {
      case 'payment.captured':
        await handlePaymentCaptured(payload.payment.entity);
        break;
      
      case 'payment.failed':
        await handlePaymentFailed(payload.payment.entity);
        break;
      
      case 'order.paid':
        await handleOrderPaid(payload.order.entity, payload.payment.entity);
        break;
      
      default:
        console.log(`ℹ️ [Webhook] Unhandled event type: ${event}`);
    }

    return res.status(200).json({
      success: true,
      message: 'Webhook processed successfully'
    });

  } catch (error) {
    console.error('❌ [Webhook] Error processing webhook:', error);
    return res.status(500).json({
      success: false,
      error: error.message || 'Webhook processing failed'
    });
  }
};

// Handle payment captured event
async function handlePaymentCaptured(payment) {
  try {
    console.log('💰 [Webhook] Processing payment.captured event');
    console.log('💰 [Webhook] Payment ID:', payment.id);
    console.log('💰 [Webhook] Order ID:', payment.order_id);
    console.log('💰 [Webhook] Amount:', payment.amount);
    console.log('💰 [Webhook] Status:', payment.status);

    // Extract order ID from notes if available
    const orderIdFromNotes = payment.notes?.order_id;

    if (orderIdFromNotes) {
      console.log('📝 [Webhook] Found order ID in notes:', orderIdFromNotes);

      // Update payment record in database
      const { data: paymentRecord, error: paymentError } = await supabase
        .from('razorpay_payments')
        .upsert({
          razorpay_order_id: payment.order_id,
          razorpay_payment_id: payment.id,
          amount: payment.amount / 100, // Convert from paise to rupees
          currency: payment.currency,
          status: 'captured',
          method: payment.method,
          user_id: payment.notes?.user_id,
          created_at: new Date(payment.created_at * 1000).toISOString(),
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'razorpay_payment_id'
        });

      if (paymentError) {
        console.error('❌ [Webhook] Error updating payment record:', paymentError);
      } else {
        console.log('✅ [Webhook] Payment record updated successfully');
      }

      // If this is a temporary order, trigger order creation
      if (orderIdFromNotes.startsWith('temp_')) {
        console.log('📦 [Webhook] Temporary order detected, will be handled by frontend');
      } else {
        // Update existing order payment status
        const { error: orderError } = await supabase
          .from('orders')
          .update({
            payment_status: 'completed',
            razorpay_payment_id: payment.id,
            updated_at: new Date().toISOString()
          })
          .eq('id', orderIdFromNotes);

        if (orderError) {
          console.error('❌ [Webhook] Error updating order:', orderError);
        } else {
          console.log('✅ [Webhook] Order payment status updated');
        }
      }

      console.log('✅ [Webhook] Payment captured successfully processed');
    } else {
      console.warn('⚠️ [Webhook] No order ID found in payment notes');
    }

  } catch (error) {
    console.error('❌ [Webhook] Error handling payment.captured:', error);
    throw error;
  }
}

// Handle payment failed event
async function handlePaymentFailed(payment) {
  try {
    console.log('❌ [Webhook] Processing payment.failed event');
    console.log('❌ [Webhook] Payment ID:', payment.id);
    console.log('❌ [Webhook] Order ID:', payment.order_id);
    console.log('❌ [Webhook] Error:', payment.error_description);

    // Extract order ID from notes if available
    const orderIdFromNotes = payment.notes?.order_id;
    
    if (orderIdFromNotes) {
      console.log('📝 [Webhook] Found order ID in notes:', orderIdFromNotes);
      
      // Here you would typically update your database to mark payment as failed
      console.log('❌ [Webhook] Payment failed event processed');
    }

  } catch (error) {
    console.error('❌ [Webhook] Error handling payment.failed:', error);
    throw error;
  }
}

// Handle order paid event
async function handleOrderPaid(order, payment) {
  try {
    console.log('🎉 [Webhook] Processing order.paid event');
    console.log('🎉 [Webhook] Order ID:', order.id);
    console.log('🎉 [Webhook] Payment ID:', payment.id);
    console.log('🎉 [Webhook] Amount:', order.amount);

    // Extract order ID from notes if available
    const orderIdFromNotes = order.notes?.order_id || payment.notes?.order_id;
    
    if (orderIdFromNotes) {
      console.log('📝 [Webhook] Found order ID in notes:', orderIdFromNotes);
      
      // Here you would typically:
      // 1. Update order status to 'paid'
      // 2. Update payment status
      // 3. Clear user's cart
      // 4. Send confirmation email
      // 5. Trigger any post-payment workflows
      
      console.log('🎉 [Webhook] Order paid event processed successfully');
    }

  } catch (error) {
    console.error('❌ [Webhook] Error handling order.paid:', error);
    throw error;
  }
}
