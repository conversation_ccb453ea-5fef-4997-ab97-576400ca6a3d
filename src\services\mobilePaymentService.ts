/**
 * Mobile Payment Service
 * Handles mobile-specific payment data storage and device detection
 */

export interface StoredPaymentData {
  orderId: string;
  razorpayOrderId: string;
  razorpayPaymentId?: string;
  razorpaySignature?: string;
  amount: number;
  userId: string;
  timestamp: number;
  status: 'initiated' | 'completed' | 'dismissed' | 'failed';
  completedAt?: number;
  dismissedAt?: number;
  orderData?: {
    items: any[];
    shipping_address: any;
    billing_address: any;
    shipping_fee: number;
    payment_method: string;
  };
}

class MobilePaymentService {
  private static readonly STORAGE_KEY = 'mobile_payment_data';

  /**
   * Check if the current device is mobile
   */
  static isMobileDevice(): boolean {
    if (typeof window === 'undefined') return false;
    
    // Check screen width
    const isMobileWidth = window.innerWidth <= 768;
    
    // Check user agent
    const isMobileUserAgent = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    
    // Check for touch support
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    
    return isMobileWidth || (isMobileUserAgent && isTouchDevice);
  }

  /**
   * Store payment data in localStorage
   */
  static storePaymentData(data: StoredPaymentData): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(data));
      console.log('📱 Payment data stored:', data.orderId);
    } catch (error) {
      console.error('❌ Failed to store payment data:', error);
    }
  }

  /**
   * Get stored payment data from localStorage
   */
  static getStoredPaymentData(): StoredPaymentData | null {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY);
      if (data) {
        const parsed = JSON.parse(data);
        console.log('📱 Retrieved payment data:', parsed.orderId);
        return parsed;
      }
      return null;
    } catch (error) {
      console.error('❌ Failed to retrieve payment data:', error);
      return null;
    }
  }

  /**
   * Update stored payment data
   */
  static updatePaymentData(updates: Partial<StoredPaymentData>): void {
    try {
      const existingData = this.getStoredPaymentData();
      if (existingData) {
        const updatedData = { ...existingData, ...updates };
        this.storePaymentData(updatedData);
        console.log('📱 Payment data updated:', updates);
      }
    } catch (error) {
      console.error('❌ Failed to update payment data:', error);
    }
  }

  /**
   * Clear stored payment data
   */
  static clearPaymentData(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
      console.log('📱 Payment data cleared');
    } catch (error) {
      console.error('❌ Failed to clear payment data:', error);
    }
  }

  /**
   * Check if payment data exists and is valid
   */
  static hasValidPaymentData(): boolean {
    const data = this.getStoredPaymentData();
    if (!data) return false;

    // Check if data is not too old (30 minutes)
    const maxAge = 30 * 60 * 1000; // 30 minutes in milliseconds
    const isNotExpired = (Date.now() - data.timestamp) < maxAge;

    return isNotExpired;
  }

  /**
   * Get payment status from stored data
   */
  static getPaymentStatus(): string | null {
    const data = this.getStoredPaymentData();
    return data ? data.status : null;
  }
}

export default MobilePaymentService;
