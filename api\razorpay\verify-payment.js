const crypto = require('crypto');

export default async function handler(req, res) {
  // Set CORS headers
  const origin = req.headers.origin;

  if (origin && (
    origin.includes('thebadhees.com') ||
    origin.includes('localhost') ||
    origin.includes('127.0.0.1') ||
    origin.includes('vercel.app')
  )) {
    res.setHeader('Access-Control-Allow-Origin', origin);
  } else {
    res.setHeader('Access-Control-Allow-Origin', '*');
  }

  res.setHeader('Access-Control-Allow-Credentials', 'true');
  res.setHeader('Access-Control-Allow-Methods', 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Date, X-Api-Version, X-CSRF-Token, Origin');
  res.setHeader('Access-Control-Max-Age', '86400');

  // Handle OPTIONS request (preflight)
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  try {
    // Enhanced environment variable validation
    const razorpaySecret = process.env.RAZORPAY_SECRET;

    console.log('🔍 Verify payment environment check:', {
      hasSecret: !!razorpaySecret,
      nodeEnv: process.env.NODE_ENV,
      vercelEnv: process.env.VERCEL_ENV
    });

    if (!razorpaySecret) {
      console.error('❌ Missing RAZORPAY_SECRET environment variable');
      console.error('Available env vars:', Object.keys(process.env).filter(key =>
        key.includes('RAZORPAY')
      ));
      return res.status(500).json({
        success: false,
        error: 'Server configuration error: Missing payment credentials',
        debug: {
          hasSecret: !!razorpaySecret,
          env: process.env.NODE_ENV
        }
      });
    }

    // Extract payment details from request
    const {
      razorpay_order_id,
      razorpay_payment_id,
      razorpay_signature,
      order_id
    } = req.body;

    console.log('📝 Payment verification details:', {
      hasOrderId: !!razorpay_order_id,
      hasPaymentId: !!razorpay_payment_id,
      hasSignature: !!razorpay_signature,
      orderIdPrefix: razorpay_order_id ? razorpay_order_id.substring(0, 10) + '...' : 'undefined',
      paymentIdPrefix: razorpay_payment_id ? razorpay_payment_id.substring(0, 10) + '...' : 'undefined'
    });

    if (!razorpay_order_id || !razorpay_payment_id || !razorpay_signature) {
      console.error('❌ Missing payment verification parameters:', {
        razorpay_order_id: !!razorpay_order_id,
        razorpay_payment_id: !!razorpay_payment_id,
        razorpay_signature: !!razorpay_signature
      });
      return res.status(400).json({
        success: false,
        error: 'Missing required payment verification parameters'
      });
    }

    // Verify payment signature with enhanced logging
    const body = razorpay_order_id + "|" + razorpay_payment_id;
    console.log('🔐 Signature verification body:', body.substring(0, 50) + '...');

    const expectedSignature = crypto
      .createHmac('sha256', razorpaySecret)
      .update(body.toString())
      .digest('hex');

    const isAuthentic = expectedSignature === razorpay_signature;

    console.log('🔍 Signature verification:', {
      isAuthentic,
      expectedPrefix: expectedSignature.substring(0, 10) + '...',
      receivedPrefix: razorpay_signature.substring(0, 10) + '...'
    });

    if (!isAuthentic) {
      console.error('❌ Payment signature verification failed');
      return res.status(400).json({
        success: false,
        error: 'Payment verification failed: Invalid signature'
      });
    }

    console.log('✅ Payment verification successful');

    // Payment verification successful
    return res.status(200).json({
      success: true,
      message: 'Payment verified successfully',
      data: {
        razorpay_order_id,
        razorpay_payment_id,
        order_id
      }
    });

  } catch (error) {
    console.error('❌ Error verifying payment:', error);
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });

    return res.status(500).json({
      success: false,
      error: error.message || 'Payment verification failed',
      debug: {
        errorType: error.name,
        timestamp: new Date().toISOString()
      }
    });
  }
};
