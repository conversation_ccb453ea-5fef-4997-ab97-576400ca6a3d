/**
 * Payment Failure Page
 * Displays payment failure information and retry options
 */

import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { AlertCircle, RefreshCw, ArrowLeft, HelpCircle } from 'lucide-react';
import { useAuth } from '@/context/SupabaseAuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';

const PaymentFailure: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();
  
  const [isRetrying, setIsRetrying] = useState(false);

  const orderId = searchParams.get('order_id');
  const error = searchParams.get('error');

  useEffect(() => {
    if (!isAuthenticated || !user) {
      navigate('/login');
      return;
    }
  }, [isAuthenticated, user, navigate]);

  const handleRetryPayment = async () => {
    if (!orderId) {
      navigate('/cart');
      return;
    }

    setIsRetrying(true);
    
    try {
      // Navigate back to checkout with the order ID for retry
      navigate(`/checkout?retry_order=${orderId}`);
    } catch (error) {
      console.error('Error retrying payment:', error);
      setIsRetrying(false);
    }
  };

  const handleBackToCart = () => {
    navigate('/cart');
  };

  const handleContactSupport = () => {
    // Open email client or navigate to contact page
    window.location.href = 'mailto:<EMAIL>?subject=Payment Issue - Order ' + (orderId || 'Unknown');
  };

  const getErrorMessage = () => {
    if (error) {
      return decodeURIComponent(error);
    }
    return 'Your payment could not be processed. Please try again.';
  };

  const getErrorSuggestions = () => {
    const errorMsg = getErrorMessage().toLowerCase();
    
    if (errorMsg.includes('insufficient')) {
      return 'Please check your account balance and try again.';
    }
    if (errorMsg.includes('declined') || errorMsg.includes('failed')) {
      return 'Please check your card details or try a different payment method.';
    }
    if (errorMsg.includes('network') || errorMsg.includes('timeout')) {
      return 'Please check your internet connection and try again.';
    }
    if (errorMsg.includes('cancelled')) {
      return 'Payment was cancelled. You can retry the payment anytime.';
    }
    
    return 'Please try again or contact support if the issue persists.';
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-2xl mx-auto">
        {/* Failure Header */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="text-center">
              <AlertCircle className="h-16 w-16 text-red-600 mx-auto mb-4" />
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                Payment Failed
              </h1>
              <p className="text-gray-600 mb-4">
                We couldn't process your payment. Don't worry, no money has been charged.
              </p>
              
              {orderId && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                  <div className="flex items-center justify-center space-x-2 text-red-800">
                    <AlertCircle className="h-5 w-5" />
                    <span className="font-medium">Order ID: {orderId}</span>
                  </div>
                  <p className="text-sm text-red-700 mt-1">
                    Your order is saved and you can retry payment anytime.
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Error Details */}
        <Alert className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <p className="font-medium">What happened:</p>
              <p className="text-sm">{getErrorMessage()}</p>
              <p className="text-sm text-gray-600">{getErrorSuggestions()}</p>
            </div>
          </AlertDescription>
        </Alert>

        {/* Common Issues */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <HelpCircle className="h-5 w-5" />
              <span>Common Issues & Solutions</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 text-sm">
              <div>
                <h3 className="font-medium text-gray-900">Card Declined</h3>
                <p className="text-gray-600">Check card details, expiry date, and available balance.</p>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-900">Network Issues</h3>
                <p className="text-gray-600">Ensure stable internet connection and try again.</p>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-900">Bank Restrictions</h3>
                <p className="text-gray-600">Some banks block online transactions. Contact your bank if needed.</p>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-900">UPI Issues</h3>
                <p className="text-gray-600">Ensure your UPI app is updated and try again.</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <Button 
            onClick={handleRetryPayment}
            disabled={isRetrying}
            className="flex-1 flex items-center justify-center space-x-2"
          >
            {isRetrying ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            <span>{isRetrying ? 'Retrying...' : 'Retry Payment'}</span>
          </Button>
          
          <Button 
            variant="outline"
            onClick={handleBackToCart}
            className="flex-1 flex items-center justify-center space-x-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Cart</span>
          </Button>
        </div>

        {/* Support */}
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <h3 className="font-medium text-gray-900 mb-2">Need Help?</h3>
              <p className="text-sm text-gray-600 mb-4">
                If you continue to face issues, our support team is here to help.
              </p>
              <Button 
                variant="outline" 
                onClick={handleContactSupport}
                className="flex items-center space-x-2"
              >
                <HelpCircle className="h-4 w-4" />
                <span>Contact Support</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default PaymentFailure;
