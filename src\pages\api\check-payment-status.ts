import { NextApiRequest, NextApiResponse } from 'next';
import Ra<PERSON><PERSON><PERSON> from 'razorpay';

const razorpay = new Razorpay({
  key_id: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID!,
  key_secret: process.env.RAZORPAY_KEY_SECRET!,
});

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { razorpay_order_id } = req.body;

    if (!razorpay_order_id) {
      return res.status(400).json({ error: 'Order ID is required' });
    }

    console.log('🔍 Checking payment status for order:', razorpay_order_id);

    // Fetch order details from Razorpay
    const order = await razorpay.orders.fetch(razorpay_order_id);
    console.log('📋 Order details:', order);

    // Fetch payments for this order
    const payments = await razorpay.orders.fetchPayments(razorpay_order_id);
    console.log('💳 Payments for order:', payments);

    // Check if there's a successful payment
    const successfulPayment = payments.items.find((payment: any) => 
      payment.status === 'captured' || payment.status === 'authorized'
    );

    if (successfulPayment) {
      console.log('✅ Found successful payment:', successfulPayment.id);
      
      return res.status(200).json({
        success: true,
        payment: successfulPayment,
        order: order,
        signature: null // We'll handle signature verification separately if needed
      });
    } else {
      console.log('❌ No successful payment found');
      
      return res.status(200).json({
        success: false,
        message: 'No successful payment found for this order',
        payments: payments.items
      });
    }

  } catch (error) {
    console.error('❌ Error checking payment status:', error);
    
    return res.status(500).json({
      success: false,
      error: 'Failed to check payment status',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
