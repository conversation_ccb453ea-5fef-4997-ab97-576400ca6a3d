-- =====================================================
-- THE BADHEES - COMPLETE REVIEWS SYSTEM SETUP
-- =====================================================
-- This script sets up the complete product reviews system
-- Only users who have purchased products can leave reviews
-- Reviews are automatically updated in product details page

-- =====================================================
-- 1. CREATE PRODUCT_REVIEWS TABLE
-- =====================================================

-- Drop existing table if it exists (for clean setup)
DROP TABLE IF EXISTS public.product_reviews CASCADE;

-- Create product_reviews table
CREATE TABLE public.product_reviews (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    product_id UUID NOT NULL,
    user_id UUID NOT NULL,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    title TEXT,
    comment TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one review per user per product
    UNIQUE(product_id, user_id)
);

-- Add foreign key constraints
ALTER TABLE public.product_reviews 
ADD CONSTRAINT product_reviews_product_id_fkey 
FOREIGN KEY (product_id) REFERENCES public.products(id) ON DELETE CASCADE;

ALTER TABLE public.product_reviews 
ADD CONSTRAINT product_reviews_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- Add indexes for performance
CREATE INDEX idx_product_reviews_product_id ON public.product_reviews(product_id);
CREATE INDEX idx_product_reviews_user_id ON public.product_reviews(user_id);
CREATE INDEX idx_product_reviews_rating ON public.product_reviews(rating);
CREATE INDEX idx_product_reviews_created_at ON public.product_reviews(created_at);

-- =====================================================
-- 2. CREATE PRODUCT_RATING_SUMMARY VIEW
-- =====================================================

-- Drop existing view if it exists
DROP VIEW IF EXISTS public.product_rating_summary CASCADE;

-- Create view for product rating summaries
CREATE VIEW public.product_rating_summary AS
SELECT 
    p.id as product_id,
    COALESCE(ROUND(AVG(pr.rating)::numeric, 1), 0) as average_rating,
    COALESCE(COUNT(pr.rating), 0) as review_count,
    COALESCE(COUNT(pr.rating) FILTER (WHERE pr.rating = 5), 0) as five_star_count,
    COALESCE(COUNT(pr.rating) FILTER (WHERE pr.rating = 4), 0) as four_star_count,
    COALESCE(COUNT(pr.rating) FILTER (WHERE pr.rating = 3), 0) as three_star_count,
    COALESCE(COUNT(pr.rating) FILTER (WHERE pr.rating = 2), 0) as two_star_count,
    COALESCE(COUNT(pr.rating) FILTER (WHERE pr.rating = 1), 0) as one_star_count
FROM 
    public.products p
LEFT JOIN public.product_reviews pr ON p.id = pr.product_id
GROUP BY p.id;

-- =====================================================
-- 3. CREATE USER_PURCHASABLE_REVIEWS VIEW
-- =====================================================

-- Drop existing view if it exists
DROP VIEW IF EXISTS public.user_purchasable_reviews CASCADE;

-- Create view for products that users can review (purchased products)
CREATE VIEW public.user_purchasable_reviews AS
SELECT DISTINCT
    o.user_id,
    oi.product_id,
    p.name as product_name,
    o.id as order_id,
    o.created_at as purchase_date,
    CASE 
        WHEN pr.id IS NOT NULL THEN true 
        ELSE false 
    END as has_reviewed
FROM 
    public.orders o
    INNER JOIN public.order_items oi ON o.id = oi.order_id
    INNER JOIN public.products p ON oi.product_id = p.id
    LEFT JOIN public.product_reviews pr ON (oi.product_id = pr.product_id AND o.user_id = pr.user_id)
WHERE 
    o.status IN ('delivered', 'shipped') -- Only delivered/shipped orders can be reviewed
ORDER BY o.created_at DESC;

-- =====================================================
-- 4. CREATE RPC FUNCTIONS
-- =====================================================

-- Function to check if user has purchased a product
CREATE OR REPLACE FUNCTION public.has_user_purchased_product(
    input_user_id UUID, 
    input_product_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY INVOKER
AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 
        FROM public.orders o
        INNER JOIN public.order_items oi ON o.id = oi.order_id
        WHERE o.user_id = input_user_id 
        AND oi.product_id = input_product_id
        AND o.status IN ('delivered', 'shipped')
    );
END;
$$;

-- Function to add a product review (with purchase verification)
CREATE OR REPLACE FUNCTION public.add_product_review(
    p_product_id UUID,
    p_user_id UUID,
    p_rating INTEGER,
    p_title TEXT DEFAULT NULL,
    p_comment TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY INVOKER
AS $$
DECLARE
    review_id UUID;
    has_purchased BOOLEAN;
BEGIN
    -- Check if user has purchased the product
    SELECT public.has_user_purchased_product(p_user_id, p_product_id) INTO has_purchased;
    
    IF NOT has_purchased THEN
        RAISE EXCEPTION 'User has not purchased this product and cannot leave a review';
    END IF;
    
    -- Validate rating
    IF p_rating < 1 OR p_rating > 5 THEN
        RAISE EXCEPTION 'Rating must be between 1 and 5';
    END IF;
    
    -- Insert or update the review
    INSERT INTO public.product_reviews (product_id, user_id, rating, title, comment)
    VALUES (p_product_id, p_user_id, p_rating, p_title, p_comment)
    ON CONFLICT (product_id, user_id) 
    DO UPDATE SET 
        rating = EXCLUDED.rating,
        title = EXCLUDED.title,
        comment = EXCLUDED.comment,
        updated_at = NOW()
    RETURNING id INTO review_id;
    
    RETURN review_id;
END;
$$;

-- Function to get user's review for a product
CREATE OR REPLACE FUNCTION public.get_user_review(
    p_product_id UUID,
    p_user_id UUID
)
RETURNS TABLE(
    id UUID,
    rating INTEGER,
    title TEXT,
    comment TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY INVOKER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pr.id,
        pr.rating,
        pr.title,
        pr.comment,
        pr.created_at,
        pr.updated_at
    FROM public.product_reviews pr
    WHERE pr.product_id = p_product_id AND pr.user_id = p_user_id;
END;
$$;

-- =====================================================
-- 5. SET UP ROW LEVEL SECURITY (RLS)
-- =====================================================

-- Enable RLS on product_reviews table
ALTER TABLE public.product_reviews ENABLE ROW LEVEL SECURITY;

-- Policy: Anyone can view reviews (for displaying on product pages)
CREATE POLICY "Anyone can view reviews" ON public.product_reviews
    FOR SELECT USING (true);

-- Policy: Only users who purchased the product can insert reviews
CREATE POLICY "Users can insert reviews for purchased products" ON public.product_reviews
    FOR INSERT WITH CHECK (
        auth.uid() = user_id AND 
        public.has_user_purchased_product(auth.uid(), product_id)
    );

-- Policy: Users can update their own reviews
CREATE POLICY "Users can update their own reviews" ON public.product_reviews
    FOR UPDATE USING (auth.uid() = user_id);

-- Policy: Users can delete their own reviews
CREATE POLICY "Users can delete their own reviews" ON public.product_reviews
    FOR DELETE USING (auth.uid() = user_id);

-- =====================================================
-- 6. GRANT PERMISSIONS
-- =====================================================

-- Grant permissions to authenticated users
GRANT SELECT ON public.product_reviews TO anon;
GRANT SELECT ON public.product_reviews TO authenticated;
GRANT INSERT ON public.product_reviews TO authenticated;
GRANT UPDATE ON public.product_reviews TO authenticated;
GRANT DELETE ON public.product_reviews TO authenticated;

-- Grant permissions on views
GRANT SELECT ON public.product_rating_summary TO anon;
GRANT SELECT ON public.product_rating_summary TO authenticated;
GRANT SELECT ON public.user_purchasable_reviews TO authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION public.has_user_purchased_product(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.add_product_review(UUID, UUID, INTEGER, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_review(UUID, UUID) TO authenticated;

-- =====================================================
-- 7. CREATE TRIGGER FOR UPDATED_AT
-- =====================================================

-- Trigger to automatically update updated_at
CREATE TRIGGER update_product_reviews_updated_at
    BEFORE UPDATE ON public.product_reviews
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- =====================================================
-- 8. UPDATE PRODUCTS TABLE WITH RATING COLUMNS
-- =====================================================

-- Add rating and review_count columns to products table if they don't exist
DO $$
BEGIN
    -- Add rating column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'rating') THEN
        ALTER TABLE public.products ADD COLUMN rating DECIMAL(2,1) DEFAULT 0;
    END IF;
    
    -- Add review_count column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'review_count') THEN
        ALTER TABLE public.products ADD COLUMN review_count INTEGER DEFAULT 0;
    END IF;
END $$;

-- =====================================================
-- 9. CREATE FUNCTION TO UPDATE PRODUCT RATINGS
-- =====================================================

-- Function to update product rating and review count
CREATE OR REPLACE FUNCTION public.update_product_rating(p_product_id UUID)
RETURNS VOID
LANGUAGE plpgsql
SECURITY INVOKER
AS $$
DECLARE
    avg_rating DECIMAL(2,1);
    total_reviews INTEGER;
BEGIN
    -- Calculate average rating and review count
    SELECT 
        COALESCE(ROUND(AVG(rating)::numeric, 1), 0),
        COALESCE(COUNT(*), 0)
    INTO avg_rating, total_reviews
    FROM public.product_reviews
    WHERE product_id = p_product_id;
    
    -- Update the products table
    UPDATE public.products
    SET 
        rating = avg_rating,
        review_count = total_reviews,
        updated_at = NOW()
    WHERE id = p_product_id;
END;
$$;

-- Grant execute permission on the update function
GRANT EXECUTE ON FUNCTION public.update_product_rating(UUID) TO authenticated;

-- =====================================================
-- 10. CREATE TRIGGERS TO AUTO-UPDATE PRODUCT RATINGS
-- =====================================================

-- Function to trigger product rating update
CREATE OR REPLACE FUNCTION public.trigger_update_product_rating()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    -- Update rating for the affected product
    IF TG_OP = 'DELETE' THEN
        PERFORM public.update_product_rating(OLD.product_id);
        RETURN OLD;
    ELSE
        PERFORM public.update_product_rating(NEW.product_id);
        RETURN NEW;
    END IF;
END;
$$;

-- Create triggers for automatic rating updates
CREATE TRIGGER trigger_product_reviews_insert_update_rating
    AFTER INSERT ON public.product_reviews
    FOR EACH ROW
    EXECUTE FUNCTION public.trigger_update_product_rating();

CREATE TRIGGER trigger_product_reviews_update_update_rating
    AFTER UPDATE ON public.product_reviews
    FOR EACH ROW
    EXECUTE FUNCTION public.trigger_update_product_rating();

CREATE TRIGGER trigger_product_reviews_delete_update_rating
    AFTER DELETE ON public.product_reviews
    FOR EACH ROW
    EXECUTE FUNCTION public.trigger_update_product_rating();

-- =====================================================
-- 11. INITIAL DATA UPDATE (Update existing products)
-- =====================================================

-- Update all existing products with calculated ratings
DO $$
DECLARE
    product_record RECORD;
BEGIN
    FOR product_record IN SELECT id FROM public.products LOOP
        PERFORM public.update_product_rating(product_record.id);
    END LOOP;
END $$;

-- =====================================================
-- SETUP COMPLETE!
-- =====================================================

-- Verify the setup
SELECT 'Reviews system setup completed successfully!' as status;

-- Check if tables and views are working
SELECT COUNT(*) as total_products_with_rating_view FROM public.product_rating_summary;

-- Check if functions are created
SELECT 'Functions created: has_user_purchased_product, add_product_review, get_user_review, update_product_rating' as functions_status;
